# Timesheet Excel Export Feature

## Overview
The Timesheet Excel Export feature allows users to export timesheet data and attendance records to a formatted Excel spreadsheet directly from the View Details Modal.

## How to Use
1. Navigate to the Timesheets page
2. Click "View Details" on any timesheet
3. In the View Details Modal, click the "Export to Excel" button
4. The Excel file will be automatically downloaded to your browser's default download location

## Excel File Contents

### Timesheet Details Section
- **Student Name**: Name of the student
- **Tutor Name**: Assigned tutor
- **Subject**: Subject being taught
- **Start Date**: When the timesheet period begins
- **Duration**: Class duration in minutes
- **Contact Number**: Student/parent contact information
- **Created Date**: When the timesheet was created
- **Notes**: Any additional notes about the timesheet

### Attendance Records Section
- **Date & Time**: When each lesson occurred
- **Present**: Whether the student attended (Yes/No)
- **Signature**: Tutor's signature or initials
- **Notes**: Any notes about the specific lesson
- **Created**: When the attendance record was added

## File Naming Convention
Files are automatically named using the pattern:
```
Timesheet_{StudentName}_{Date}.xlsx
```
Example: `Timesheet_John_Smith_2025-01-29.xlsx`

## Technical Implementation

### Dependencies
- **Syncfusion.XlsIO.Net.Core**: Excel generation library
- **Custom JavaScript**: File download functionality

### Key Components
- **ExportTimesheetToExcel()**: C# method that generates the Excel file
- **downloadFile()**: JavaScript function that handles browser download
- **Professional Styling**: Headers, colors, and formatting applied automatically

### File Format
- **Excel Format**: .xlsx (Excel 2007+)
- **MIME Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Encoding**: Base64 for transfer, binary for download

## Features
- ✅ Professional formatting with styled headers
- ✅ Automatic column width adjustment
- ✅ Comprehensive data export (header + entries)
- ✅ Error handling and user feedback
- ✅ Automatic filename generation
- ✅ Browser-compatible download

## User Permissions
The export functionality respects the same permissions as viewing timesheets:
- **Administrators**: Can export any timesheet
- **Tutors**: Can export their own timesheets only
- **Students**: Can export their own timesheets only

## Browser Compatibility
The export feature works with all modern browsers that support:
- Blob API
- Base64 decoding
- Automatic file downloads

## Troubleshooting
If the export doesn't work:
1. Ensure JavaScript is enabled in your browser
2. Check that pop-up blockers aren't preventing the download
3. Verify you have permission to view the timesheet
4. Try refreshing the page and attempting the export again
