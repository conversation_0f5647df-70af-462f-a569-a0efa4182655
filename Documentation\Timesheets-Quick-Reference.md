# Timesheet System - Quick Reference Guide

## Getting Started

### Accessing Timesheets
1. Log in to the Shining C Music School system
2. Click **"Manage Timesheets"** on the home page, or
3. Navigate to `/timesheets` in your browser

### What You'll See
- **Administrators**: All students in the system
- **Tutors**: Only students assigned to you

## Quick Actions

### ➕ Create New Timesheet
1. Click **"Add Timesheet"** button
2. Select student from dropdown
3. Set start date and class duration (minutes)
4. Click **"Add"** to save

### 📝 Record Attendance
1. Click on any student row to view their timesheet
2. Click **"Add Entry"** in the attendance section
3. Set date and time (15-minute intervals)
4. Check "Present" if student attended
5. Add your signature/initials
6. Add notes if needed
7. Click **"Add"** to save

### ✏️ Edit Attendance
1. In the attendance records grid, click the **edit** icon
2. Modify any field as needed
3. Click **"Update"** to save changes

### 🗑️ Delete Records
1. Click the **delete** icon next to any entry
2. Confirm deletion in the popup dialog

## Field Explanations

### Timesheet Fields
- **Student**: The student this timesheet belongs to
- **Start Date**: When the timesheet period begins
- **Class Duration**: Length of each lesson in minutes

### Attendance Entry Fields
- **Date & Time**: When the lesson occurred (required)
- **Present**: Check if student attended, uncheck if absent
- **Signature**: Your initials or signature (optional)
- **Notes**: Any observations or comments (optional)

## Tips for Effective Use

### ✅ Best Practices
- **Record promptly**: Enter attendance right after each lesson
- **Be consistent**: Use the same signature format each time
- **Use notes**: Record important observations about student progress
- **Check accuracy**: Verify date/time before saving entries

### ⚠️ Important Notes
- You can only see students assigned to you (tutors)
- Administrators can see all students
- Deleted entries cannot be recovered
- All times are recorded in 15-minute intervals

## Common Tasks

### Weekly Attendance Entry
1. Go to student's timesheet
2. Add entry for each lesson that week
3. Set correct date/time for each lesson
4. Mark present/absent as appropriate
5. Add any relevant notes

### Monthly Review
1. Review all attendance entries for accuracy
2. Edit any incorrect dates or times
3. Add missing entries for lessons that occurred
4. Remove any duplicate or incorrect entries

### Timesheet Setup for New Student
1. Ensure student is created in the system
2. Create new timesheet with appropriate start date
3. Set correct class duration for their lesson type
4. Begin recording attendance entries

## Keyboard Shortcuts

- **Tab**: Move between form fields
- **Enter**: Submit forms (when focused on submit button)
- **Escape**: Close modal dialogs
- **Space**: Toggle checkboxes

## Mobile Usage

### Phone/Tablet Tips
- Tap student rows to view timesheets
- Use date/time picker by tapping the field
- Swipe in grids to see all columns
- Use landscape mode for better grid viewing

## Troubleshooting

### Can't See a Student?
- **Tutors**: Student must be assigned to you
- **Check assignments**: Contact admin to verify student assignments

### Entry Won't Save?
- Check that date/time is selected
- Ensure you have permission to edit this student's timesheet
- Verify network connection

### Missing Data?
- Refresh the page
- Check if you're looking at the correct student
- Verify date range if filtering is applied

## Getting Help

### Need Support?
- Contact your system administrator
- Check the full documentation at `Documentation/Timesheets.md`
- Report bugs or issues to technical support

### Training Resources
- User training sessions available on request
- Video tutorials (if available)
- Practice with test data before using live student records

---

**Remember**: This system replaces paper timesheets - all attendance tracking should be done digitally for accurate record-keeping and easy access by authorized staff.
