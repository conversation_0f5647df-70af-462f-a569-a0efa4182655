# Timesheet System - Technical Implementation Guide

## Architecture Overview

The timesheet system follows the established patterns in the Shining C Music School application:

- **Frontend**: <PERSON><PERSON>zor WebAssembly with Syncfusion components
- **Backend**: ASP.NET Core Web API
- **Database**: SQL Server with Dapper ORM
- **Authentication**: JWT-based with role-based authorization

## Project Structure

### Models (`ShiningCMusicCommon/Models/`)
- `Timesheet.cs` - Main timesheet entity
- `TimesheetEntry.cs` - Individual attendance records

### API Services (`ShiningCMusicApi/Services/`)
- `ITimesheetService.cs` - Service interface
- `Implementations/TimesheetService.cs` - Service implementation

### Client Services (`ShiningCMusicApp/Services/`)
- `ITimesheetClientService.cs` - Client interface
- `Implementations/TimesheetClientService.cs` - HTTP client implementation

### UI Components (`ShiningCMusicApp/Pages/`)
- `Timesheets.razor` - Main page component
- `Timesheets.razor.cs` - Code-behind logic
- `Timesheets.razor.css` - Component-specific styles

## Database Schema

### Timesheets Table
```sql
CREATE TABLE [dbo].[Timesheets] (
    [TimesheetId] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [StudentId] int NOT NULL,
    [StartDate] datetime NOT NULL,
    [ClassDurationMinutes] int NOT NULL,
    [CreatedUTC] datetime NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedUTC] datetime NULL,
    CONSTRAINT [FK_Timesheets_Students] FOREIGN KEY ([StudentId]) 
        REFERENCES [dbo].[Students] ([StudentId]) ON DELETE CASCADE
);
```

### TimesheetEntries Table
```sql
CREATE TABLE [dbo].[TimesheetEntries] (
    [TimesheetEntryId] int IDENTITY(1,1) NOT NULL PRIMARY KEY,
    [TimesheetId] int NOT NULL,
    [AttendanceDateTime] datetime NOT NULL,
    [IsPresent] bit NOT NULL DEFAULT 1,
    [Signature] nvarchar(100) NULL,
    [Notes] nvarchar(500) NULL,
    [CreatedUTC] datetime NOT NULL DEFAULT GETUTCDATE(),
    [UpdatedUTC] datetime NULL,
    CONSTRAINT [FK_TimesheetEntries_Timesheets] FOREIGN KEY ([TimesheetId]) 
        REFERENCES [dbo].[Timesheets] ([TimesheetId]) ON DELETE CASCADE
);
```

## API Implementation

### Controller (`TimesheetController.cs`)
```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize]
public class TimesheetController : ControllerBase
{
    // GET /api/timesheet - Get all accessible timesheets
    // GET /api/timesheet/{id} - Get specific timesheet
    // POST /api/timesheet - Create new timesheet
    // PUT /api/timesheet/{id} - Update timesheet
    // DELETE /api/timesheet/{id} - Delete timesheet
    
    // GET /api/timesheet/{id}/entries - Get timesheet entries
    // POST /api/timesheet/entries - Create entry
    // PUT /api/timesheet/entries/{id} - Update entry
    // DELETE /api/timesheet/entries/{id} - Delete entry
}
```

### Service Layer
The `TimesheetService` implements all CRUD operations with role-based filtering:

```csharp
public async Task<IEnumerable<Timesheet>> GetTimesheetsAsync(string userRole, int? tutorId)
{
    // Filters timesheets based on user role and tutor assignment
    // Administrators see all, tutors see only their assigned students
}
```

## Client-Side Implementation

### Blazor Component Structure
```csharp
public partial class Timesheets : ComponentBase
{
    // Properties for data binding
    private List<Student> students = new();
    private List<Timesheet> timesheets = new();
    private List<TimesheetEntry> timesheetEntries = new();
    
    // Modal state management
    private bool showTimesheetModal = false;
    private bool showEntryModal = false;
    
    // Current editing objects
    private Timesheet currentTimesheet = new();
    private TimesheetEntry currentEntry = new();
}
```

### Key Methods
- `LoadStudentsAsync()` - Loads students based on user role
- `LoadTimesheetsAsync()` - Loads timesheets with role filtering
- `SelectStudent()` - Handles student selection and loads timesheet data
- `SaveTimesheet()` - Creates/updates timesheet records
- `SaveTimesheetEntry()` - Creates/updates attendance entries

## Security Implementation

### Role-Based Access Control
```csharp
// In API service
public async Task<IEnumerable<Timesheet>> GetTimesheetsAsync(string userRole, int? tutorId)
{
    var sql = userRole == "Administrator" 
        ? "SELECT * FROM Timesheets t INNER JOIN Students s ON t.StudentId = s.StudentId"
        : "SELECT * FROM Timesheets t INNER JOIN Students s ON t.StudentId = s.StudentId WHERE s.TutorId = @TutorId";
    
    return await connection.QueryAsync<Timesheet>(sql, new { TutorId = tutorId });
}
```

### Client-Side Authorization
```csharp
// In Blazor component
protected override async Task OnInitializedAsync()
{
    var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
    var user = authState.User;
    
    if (user.IsInRole("Administrator"))
    {
        CanEditAllTimesheets = true;
        await LoadAllStudentsAsync();
    }
    else if (user.IsInRole("Tutor"))
    {
        CanEditAllTimesheets = false;
        await LoadTutorStudentsAsync();
    }
}
```

## UI Components and Styling

### Syncfusion Components Used
- `SfGrid` - Data display with sorting and pagination
- `SfDialog` - Modal dialogs for forms
- `SfDateTimePicker` - Combined date/time input
- `SfDropDownList` - Student selection
- `SfTextBox` - Text inputs
- `SfCheckBox` - Attendance status
- `SfButton` - Action buttons

### Responsive Design
```css
/* Mobile-first responsive grid */
@media (max-width: 768px) {
    .timesheet-grid {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}
```

### Custom CSS Classes
- `.btn-blue-custom` - Primary action buttons
- `.btn-cancel-custom` - Cancel/secondary buttons
- `.form-control` - Consistent form styling

## Data Flow

### Timesheet Creation Flow
1. User clicks "Add Timesheet"
2. Modal opens with student dropdown (filtered by role)
3. User selects student, sets start date and duration
4. Form validation ensures required fields
5. API call creates timesheet record
6. UI refreshes to show new timesheet

### Attendance Entry Flow
1. User selects student to view timesheet
2. Timesheet details load with existing entries
3. User clicks "Add Entry" to record attendance
4. Date/time picker allows precise scheduling
5. Attendance status and notes captured
6. Entry saved via API and grid refreshes

## Error Handling

### API Error Responses
```csharp
try
{
    // Service operation
    return Ok(result);
}
catch (UnauthorizedAccessException)
{
    return Forbid();
}
catch (ArgumentException ex)
{
    return BadRequest(ex.Message);
}
catch (Exception ex)
{
    logger.LogError(ex, "Error in timesheet operation");
    return StatusCode(500, "Internal server error");
}
```

### Client Error Handling
```csharp
try
{
    await TimesheetClientService.CreateTimesheetAsync(timesheet);
    await DialogService.AlertAsync("Success", "Timesheet created successfully");
}
catch (HttpRequestException ex)
{
    await DialogService.AlertAsync("Error", $"Failed to create timesheet: {ex.Message}");
}
```

## Performance Considerations

### Database Optimization
- Indexes on foreign keys (`StudentId`, `TimesheetId`)
- Efficient JOIN queries for data retrieval
- Pagination in grid components to limit data transfer

### Client-Side Optimization
- Lazy loading of timesheet entries
- Efficient state management in Blazor components
- Minimal re-renders through proper data binding

## Testing Strategy

### Unit Tests
- Service layer methods with mock data
- Validation logic testing
- Role-based filtering verification

### Integration Tests
- API endpoint testing with different user roles
- Database operations with test data
- End-to-end workflow testing

### UI Testing
- Component rendering tests
- Form validation testing
- Modal dialog functionality

## Deployment Considerations

### Database Migration
1. Run SQL scripts to create tables
2. Update existing `AttendanceDate` to `AttendanceDateTime` if upgrading
3. Verify foreign key constraints

### Configuration Updates
- Ensure timesheet routes are registered
- Verify authorization policies
- Update navigation menu if needed

### Monitoring
- Log timesheet operations for audit trails
- Monitor API performance for large datasets
- Track user adoption and usage patterns

## Future Enhancements

### Potential Features
- Bulk attendance entry for multiple students
- Attendance reporting and analytics
- Export timesheets to PDF or Excel
- Automated attendance reminders
- Integration with lesson scheduling system

### Technical Improvements
- Real-time updates using SignalR
- Offline capability with local storage
- Advanced filtering and search options
- Audit trail for all timesheet changes
