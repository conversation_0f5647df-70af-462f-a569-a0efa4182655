# Timesheet Management System

## Overview

The Timesheet Management System provides digital attendance tracking for music students, replacing traditional paper-based timesheet records. The system allows tutors and administrators to create, manage, and track student attendance with detailed records including date, time, signatures, and notes.

## Features

### Core Functionality
- **Digital Timesheets**: Create and manage electronic timesheet records for each student
- **Attendance Tracking**: Record attendance with date, time, presence status, signatures, and notes
- **Role-Based Access**: Different access levels for administrators and tutors
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices

### User Roles and Permissions

#### Administrator
- View all students' timesheets across the entire system
- Create, edit, and delete timesheets for any student
- Manage timesheet entries for all students
- Full CRUD operations on all timesheet data

#### Tutor
- View timesheets only for students assigned to them
- Create, edit, and delete timesheets for their assigned students
- Manage attendance entries for their students only
- Cannot access other tutors' student timesheets

#### Student
- No direct access to timesheet management
- Timesheets are managed by their assigned tutors or administrators

## User Interface

### Main Timesheet Page (`/timesheets`)

#### Student Selection
- **Grid Display**: Shows all accessible students in a sortable, paginated grid
- **Student Information**: Displays student name, contact number, and assigned tutor
- **Quick Actions**: Click on any student row to view their timesheet details

#### Timesheet Management
- **Timesheet Creation**: Create new timesheets for students who don't have active records
- **Timesheet Details**: View comprehensive timesheet information including:
  - Student details (name, contact, tutor)
  - Timesheet metadata (start date, class duration)
  - Complete attendance history

#### Attendance Entry Management
- **Add Entries**: Record new attendance with date/time picker
- **Edit Entries**: Modify existing attendance records
- **Delete Entries**: Remove incorrect or duplicate entries
- **Bulk Operations**: Efficient management of multiple attendance records

### Modal Dialogs

#### Add/Edit Timesheet Modal
- **Student Selection**: Choose from available students (filtered by role permissions)
- **Start Date**: Set the timesheet start date
- **Class Duration**: Specify lesson duration in minutes
- **Form Validation**: Ensures all required fields are completed

#### Add/Edit Entry Modal
- **Date & Time**: Combined date/time picker with 15-minute intervals
- **Attendance Status**: Checkbox to mark student as present/absent
- **Signature**: Text field for tutor signature or initials
- **Notes**: Optional field for additional comments or observations

## Technical Implementation

### Database Schema

#### Timesheets Table
```sql
TimesheetId (int, Primary Key, Identity)
StudentId (int, Foreign Key to Students)
StartDate (datetime, Required)
ClassDurationMinutes (int, Required)
CreatedUTC (datetime, Auto-generated)
UpdatedUTC (datetime, Auto-updated)
```

#### TimesheetEntries Table
```sql
TimesheetEntryId (int, Primary Key, Identity)
TimesheetId (int, Foreign Key to Timesheets)
AttendanceDateTime (datetime, Required)
IsPresent (bit, Required)
Signature (nvarchar(100), Optional)
Notes (nvarchar(500), Optional)
CreatedUTC (datetime, Auto-generated)
UpdatedUTC (datetime, Auto-updated)
```

### API Endpoints

#### Timesheet Management
- `GET /api/timesheets` - Get all accessible timesheets (filtered by user role)
- `GET /api/timesheets/{id}` - Get specific timesheet details
- `POST /api/timesheets` - Create new timesheet
- `PUT /api/timesheets/{id}` - Update existing timesheet
- `DELETE /api/timesheets/{id}` - Delete timesheet and all entries

#### Timesheet Entry Management
- `GET /api/timesheets/{id}/entries` - Get all entries for a timesheet
- `POST /api/timesheets/entries` - Create new attendance entry
- `PUT /api/timesheets/entries/{id}` - Update existing entry
- `DELETE /api/timesheets/entries/{id}` - Delete attendance entry

### Security and Authorization

#### Role-Based Filtering
- **Data Access**: Users only see timesheets for students they're authorized to manage
- **API Security**: All endpoints validate user permissions before data access
- **Client-Side Protection**: UI elements are conditionally rendered based on user roles

#### Data Validation
- **Server-Side**: Comprehensive validation in API controllers and services
- **Client-Side**: Real-time form validation using Blazor data annotations
- **Database**: Constraints and foreign key relationships ensure data integrity

## Usage Workflows

### Creating a New Timesheet
1. Navigate to Timesheets page
2. Click "Add Timesheet" button
3. Select student from dropdown (filtered by permissions)
4. Set start date and class duration
5. Save to create the timesheet record

### Recording Attendance
1. Select student from the grid to view their timesheet
2. Click "Add Entry" in the attendance records section
3. Set date and time using the date/time picker
4. Mark attendance status (present/absent)
5. Add signature and any relevant notes
6. Save the attendance entry

### Managing Existing Records
1. View timesheet details for any student
2. Use edit/delete actions on individual attendance entries
3. Modify timesheet details using the edit timesheet option
4. Delete entire timesheets if no longer needed

## Best Practices

### Data Entry
- **Consistent Signatures**: Use standardized signature format (initials or full name)
- **Timely Recording**: Enter attendance data promptly after lessons
- **Detailed Notes**: Include relevant observations about student progress or behavior
- **Accurate Timing**: Use precise date/time entries for accurate record-keeping

### System Administration
- **Regular Backups**: Ensure timesheet data is included in regular database backups
- **User Training**: Provide training for tutors on proper timesheet usage
- **Data Review**: Periodically review timesheet data for accuracy and completeness
- **Access Management**: Regularly audit user permissions and student assignments

## Troubleshooting

### Common Issues
- **Missing Students**: Ensure students are properly assigned to tutors in the system
- **Permission Errors**: Verify user roles and student assignments
- **Data Not Saving**: Check network connectivity and form validation errors
- **Display Issues**: Clear browser cache and ensure JavaScript is enabled

### Support
For technical issues or questions about the timesheet system, contact the system administrator or refer to the main application documentation.
