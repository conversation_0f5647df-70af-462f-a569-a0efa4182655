# CORS Configuration
# Primary method: Environment variable with comma-separated origins (takes precedence)
CORS_ALLOWED_ORIGINS=https://your-production-domain.com,https://your-staging-domain.com

# Alternative method: Configuration file settings (fallback)
# CorsSettings__AllowedOrigins__0=https://your-production-domain.com
# CorsSettings__AllowedOrigins__1=https://your-staging-domain.com

# For Azure App Service, set this as an Application Setting:
# CORS_ALLOWED_ORIGINS = https://your-production-domain.com,https://your-staging-domain.com
