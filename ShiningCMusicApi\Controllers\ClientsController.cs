using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Abstractions;
using ShiningCMusicApi.Services.Interfaces;
using System.Text.Json;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace ShiningCMusicApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize] // Require authentication for all endpoints
public class ClientsController : ControllerBase
{
    private readonly IOpenIddictApplicationManager _applicationManager;
    private readonly IClientSecretService _clientSecretService;
    private readonly ILogger<ClientsController> _logger;

    public ClientsController(
        IOpenIddictApplicationManager applicationManager,
        IClientSecretService clientSecretService,
        ILogger<ClientsController> logger)
    {
        _applicationManager = applicationManager;
        _clientSecretService = clientSecretService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new OpenIddict client
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> CreateClient([FromBody] CreateClientRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.ClientId))
            {
                return BadRequest(new { message = "ClientId is required" });
            }

            if (string.IsNullOrWhiteSpace(request.ClientSecret))
            {
                return BadRequest(new { message = "ClientSecret is required" });
            }

            // Check if client already exists
            if (await _applicationManager.FindByClientIdAsync(request.ClientId) != null)
            {
                return Conflict(new { message = $"Client '{request.ClientId}' already exists" });
            }

            // Create OpenIddict application
            var descriptor = new OpenIddictApplicationDescriptor
            {
                ClientId = request.ClientId,
                ClientSecret = request.ClientSecret,
                ConsentType = ConsentTypes.Implicit,
                DisplayName = request.DisplayName ?? request.ClientId,
                ClientType = ClientTypes.Confidential,
                Properties =
                {
                    ["access_token_lifetime"] = JsonSerializer.SerializeToElement(request.TokenLifetimeSeconds.ToString())
                },
                Permissions =
                {
                    Permissions.Endpoints.Token,
                    Permissions.GrantTypes.ClientCredentials,
                    Permissions.Prefixes.Scope + "ShiningCMusicApi"
                }
            };

            await _applicationManager.CreateAsync(descriptor);

            // Also create in ClientSecrets table for token lifetime management
            await _clientSecretService.CreateClientSecretAsync(
                request.ClientId, 
                request.ClientSecret, 
                request.Description, 
                request.TokenLifetimeSeconds);

            _logger.LogInformation("Created new client: {ClientId}", request.ClientId);

            return Ok(new 
            { 
                message = "Client created successfully", 
                clientId = request.ClientId,
                tokenLifetimeSeconds = request.TokenLifetimeSeconds
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating client: {ClientId}", request.ClientId);
            return StatusCode(500, new { message = "Internal server error" });
        }
    }

    /// <summary>
    /// Get all clients
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetClients()
    {
        try
        {
            var clients = new List<object>();
            
            await foreach (var application in _applicationManager.ListAsync())
            {
                var clientId = await _applicationManager.GetClientIdAsync(application);
                var displayName = await _applicationManager.GetDisplayNameAsync(application);
                
                // Get token lifetime from ClientSecrets table
                var tokenLifetime = await _clientSecretService.GetTokenLifetimeAsync(clientId!);
                
                clients.Add(new
                {
                    clientId,
                    displayName,
                    tokenLifetimeSeconds = tokenLifetime
                });
            }

            return Ok(clients);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving clients");
            return StatusCode(500, new { message = "Internal server error" });
        }
    }

    /// <summary>
    /// Get a specific client by ID
    /// </summary>
    [HttpGet("{clientId}")]
    public async Task<IActionResult> GetClient(string clientId)
    {
        try
        {
            var application = await _applicationManager.FindByClientIdAsync(clientId);
            if (application == null)
            {
                return NotFound(new { message = $"Client '{clientId}' not found" });
            }

            var displayName = await _applicationManager.GetDisplayNameAsync(application);
            var tokenLifetime = await _clientSecretService.GetTokenLifetimeAsync(clientId);

            return Ok(new
            {
                clientId,
                displayName,
                tokenLifetimeSeconds = tokenLifetime
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving client: {ClientId}", clientId);
            return StatusCode(500, new { message = "Internal server error" });
        }
    }

    /// <summary>
    /// Delete a client
    /// </summary>
    [HttpDelete("{clientId}")]
    public async Task<IActionResult> DeleteClient(string clientId)
    {
        try
        {
            var application = await _applicationManager.FindByClientIdAsync(clientId);
            if (application == null)
            {
                return NotFound(new { message = $"Client '{clientId}' not found" });
            }

            // Delete from OpenIddict
            await _applicationManager.DeleteAsync(application);

            // Delete from ClientSecrets table
            await _clientSecretService.DeleteClientSecretAsync(clientId);

            _logger.LogInformation("Deleted client: {ClientId}", clientId);

            return Ok(new { message = "Client deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting client: {ClientId}", clientId);
            return StatusCode(500, new { message = "Internal server error" });
        }
    }
}

public class CreateClientRequest
{
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public int TokenLifetimeSeconds { get; set; } = 3600; // Default 1 hour
}
