using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ConfigController : ControllerBase
    {
        private readonly IConfigService _configService;
        private readonly ILogger<ConfigController> _logger;

        public ConfigController(IConfigService configService, ILogger<ConfigController> logger)
        {
            _configService = configService;
            _logger = logger;
        }

        /// <summary>
        /// Get all configuration groups
        /// </summary>
        [HttpGet("groups")]
        public async Task<ActionResult<IEnumerable<ConfigGroup>>> GetConfigGroups()
        {
            try
            {
                var groups = await _configService.GetConfigGroupsAsync();
                return Ok(groups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving config groups");
                return StatusCode(500, new { error = "Failed to retrieve configuration groups", details = ex.Message });
            }
        }

        /// <summary>
        /// Get configuration group by ID
        /// </summary>
        [HttpGet("groups/{groupId}")]
        public async Task<ActionResult<ConfigGroup>> GetConfigGroup(int groupId)
        {
            try
            {
                var group = await _configService.GetConfigGroupAsync(groupId);
                if (group == null)
                {
                    return NotFound(new { error = $"Configuration group '{groupId}' not found" });
                }
                return Ok(group);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving config group {GroupId}", groupId);
                return StatusCode(500, new { error = "Failed to retrieve configuration group", details = ex.Message });
            }
        }

        /// <summary>
        /// Get all configurations
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Config>>> GetAllConfigs()
        {
            try
            {
                var configs = await _configService.GetAllConfigsAsync();
                return Ok(configs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all configs");
                return StatusCode(500, new { error = "Failed to retrieve configurations", details = ex.Message });
            }
        }

        /// <summary>
        /// Get specific configuration value
        /// </summary>
        [HttpGet("{groupId}/{key}")]
        public async Task<ActionResult<Config>> GetConfig(int groupId, string key)
        {
            try
            {
                var config = await _configService.GetConfigAsync(groupId, key);
                if (config == null)
                {
                    return NotFound(new { error = $"Configuration '{groupId}:{key}' not found" });
                }
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving config {GroupId}:{Key}", groupId, key);
                return StatusCode(500, new { error = "Failed to retrieve configuration", details = ex.Message });
            }
        }

        /// <summary>
        /// Update configuration value
        /// </summary>
        [HttpPut("{configId}")]
        public async Task<ActionResult> UpdateConfig(int configId, [FromBody] ConfigUpdateRequest request)
        {
            try
            {
                if (request.ConfigId != configId)
                {
                    return BadRequest(new { error = "Config ID mismatch" });
                }

                var success = await _configService.UpdateConfigAsync(configId, request.Value);
                if (!success)
                {
                    return NotFound(new { error = $"Configuration with ID {configId} not found" });
                }

                _logger.LogInformation("Configuration {ConfigId} updated successfully", configId);
                return Ok(new { message = "Configuration updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating config {ConfigId}", configId);
                return StatusCode(500, new { error = "Failed to update configuration", details = ex.Message });
            }
        }

        /// <summary>
        /// Update multiple configurations
        /// </summary>
        [HttpPut("batch")]
        public async Task<ActionResult> UpdateConfigs([FromBody] List<ConfigUpdateRequest> requests)
        {
            try
            {
                var results = new List<object>();
                foreach (var request in requests)
                {
                    var success = await _configService.UpdateConfigAsync(request.ConfigId, request.Value);
                    results.Add(new { configId = request.ConfigId, success });
                }

                _logger.LogInformation("Batch update completed for {Count} configurations", requests.Count);
                return Ok(new { message = "Batch update completed", results });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during batch config update");
                return StatusCode(500, new { error = "Failed to update configurations", details = ex.Message });
            }
        }

        /// <summary>
        /// Create new configuration
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<Config>> CreateConfig([FromBody] Config config)
        {
            try
            {
                var createdConfig = await _configService.CreateConfigAsync(config);
                _logger.LogInformation("Configuration {GroupId}:{Key} created successfully", config.GroupId, config.Key);
                return CreatedAtAction(nameof(GetConfig), new { groupId = config.GroupId, key = config.Key }, createdConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating config {GroupId}:{Key}", config.GroupId, config.Key);
                return StatusCode(500, new { error = "Failed to create configuration", details = ex.Message });
            }
        }

        /// <summary>
        /// Delete configuration
        /// </summary>
        [HttpDelete("{configId}")]
        public async Task<ActionResult> DeleteConfig(int configId)
        {
            try
            {
                var success = await _configService.DeleteConfigAsync(configId);
                if (!success)
                {
                    return NotFound(new { error = $"Configuration with ID {configId} not found" });
                }

                _logger.LogInformation("Configuration {ConfigId} deleted successfully", configId);
                return Ok(new { message = "Configuration deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting config {ConfigId}", configId);
                return StatusCode(500, new { error = "Failed to delete configuration", details = ex.Message });
            }
        }

        /// <summary>
        /// Check if background processor is enabled
        /// </summary>
        [HttpGet("background-processors/{processorName}/enabled")]
        public async Task<ActionResult<bool>> IsBackgroundProcessorEnabled(string processorName)
        {
            try
            {
                var isEnabled = await _configService.IsBackgroundProcessorEnabledAsync(processorName);
                return Ok(new { processorName, isEnabled });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking background processor status {ProcessorName}", processorName);
                return StatusCode(500, new { error = "Failed to check processor status", details = ex.Message });
            }
        }
    }
}
