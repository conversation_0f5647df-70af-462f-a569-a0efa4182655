# Payment Reminder Service - Deployment Checklist

## 📋 Pre-Deployment Checklist

### ✅ Database Preparation

- [ ] **Backup Production Database**
  ```bash
  sqlcmd -S production-server -Q "BACKUP DATABASE [MusicSchool] TO DISK = 'C:\Backups\MusicSchool_PrePaymentReminder.bak'"
  ```

- [ ] **Test Database Scripts in Staging**
  ```bash
  # Test on staging environment first
  sqlcmd -S staging-server -d MusicSchool -i "SQL/Add_ExcludeEmail_Field.sql"
  sqlcmd -S staging-server -d MusicSchool -i "SQL/Add_PaymentReminder_Template.sql"
  sqlcmd -S staging-server -d MusicSchool -i "SQL/Test_PaymentReminder_Setup.sql"
  ```

- [ ] **Verify Database Schema Changes**
  ```sql
  -- Confirm ExcludeEmail field exists
  SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
  FROM INFORMATION_SCHEMA.COLUMNS
  WHERE TABLE_NAME = 'Students' AND COLUMN_NAME = 'ExcludeEmail'
  
  -- Confirm PaymentReminder template exists
  SELECT Name, Subject FROM EmailTemplates WHERE Name = 'PaymentReminder'
  ```

### ✅ Application Code Review

- [ ] **Code Changes Reviewed and Approved**
  - PaymentReminderService.cs implementation
  - LessonService.cs lesson counting logic
  - StudentService.cs ExcludeEmail support
  - EmailService.cs template integration
  - Program.cs service registration

- [ ] **Unit Tests Passing**
  ```bash
  dotnet test ShiningCMusicApi.Tests --filter "PaymentReminder"
  ```

- [ ] **Integration Tests Completed**
  - Email template rendering
  - Lesson counting accuracy
  - Service lifecycle management

### ✅ Configuration Preparation

- [ ] **Environment Variables Defined**
  ```bash
  # Production environment variables
  PAYMENT_REMINDER_INTERVAL_HOURS=24
  PAYMENT_REMINDER_LESSON_THRESHOLD=3
  PAYMENT_REMINDER_DEADLINE_DAYS=7
  ```

- [ ] **Email Service Configuration Verified**
  ```json
  {
    "EmailSettings": {
      "SmtpServer": "production-smtp-server",
      "SmtpPort": 587,
      "SenderEmail": "<EMAIL>",
      "SenderPassword": "secure-app-password"
    }
  }
  ```

- [ ] **Logging Configuration Updated**
  ```json
  {
    "Logging": {
      "LogLevel": {
        "ShiningCMusicApi.Services.BackgroundServices.PaymentReminderService": "Information"
      }
    }
  }
  ```

## 🚀 Deployment Steps

### Step 1: Database Migration

- [ ] **Execute Database Scripts on Production**
  ```bash
  # Execute in order
  sqlcmd -S production-server -d MusicSchool -i "SQL/Add_ExcludeEmail_Field.sql"
  sqlcmd -S production-server -d MusicSchool -i "SQL/Add_PaymentReminder_Template.sql"
  ```

- [ ] **Verify Database Changes**
  ```bash
  sqlcmd -S production-server -d MusicSchool -i "SQL/Test_PaymentReminder_Setup.sql"
  ```

- [ ] **Set Default Email Preferences for Existing Students**
  ```sql
  -- Optional: Set email preferences based on business rules
  UPDATE Students 
  SET ExcludeEmail = 0  -- Include all students by default
  WHERE ExcludeEmail IS NULL
  ```

### Step 2: Application Deployment

- [ ] **Deploy Application Code**
  ```bash
  # Standard deployment process
  dotnet publish -c Release -o /path/to/deployment
  ```

- [ ] **Set Environment Variables**
  ```bash
  # Azure App Service
  az webapp config appsettings set --resource-group myResourceGroup --name myApp --settings PAYMENT_REMINDER_INTERVAL_HOURS=24

  # IIS/Windows Server
  setx PAYMENT_REMINDER_INTERVAL_HOURS "24" /M
  ```

- [ ] **Restart Application**
  ```bash
  # Ensure service registration takes effect
  iisreset  # For IIS
  # or restart Azure App Service
  ```

### Step 3: Service Verification

- [ ] **Confirm Service Started**
  ```
  Check application logs for:
  "PaymentReminderService started."
  "PaymentReminderService configured with interval: X hours, lesson threshold: Y, payment deadline: Z days"
  ```

- [ ] **Test Email Template**
  ```sql
  -- Manually test template rendering
  EXEC sp_send_dbmail
    @profile_name = 'Default',
    @recipients = '<EMAIL>',
    @subject = 'Test Payment Reminder Template',
    @body = 'Testing PaymentReminder template functionality'
  ```

- [ ] **Monitor Initial Execution**
  ```
  Wait for first scheduled execution and verify:
  - Service executes without errors
  - Appropriate log messages appear
  - No unexpected exceptions
  ```

## 🔍 Post-Deployment Verification

### ✅ Functional Testing

- [ ] **Test Student Email Exclusion**
  ```sql
  -- Create test scenario
  UPDATE Students SET ExcludeEmail = 1 WHERE StudentId = @TestStudentId
  -- Verify student doesn't receive reminders
  ```

- [ ] **Test Lesson Counting Logic**
  ```sql
  -- Verify lesson counts for sample students
  SELECT s.StudentName, COUNT(l.LessonId) as FutureLessons
  FROM Students s
  LEFT JOIN Lessons l ON s.StudentId = l.StudentId 
    AND l.IsArchived = 0 AND l.StartTime > GETDATE()
  WHERE s.StudentId IN (@TestStudentIds)
  GROUP BY s.StudentId, s.StudentName
  ```

- [ ] **Test Email Delivery**
  ```
  Monitor email delivery for first batch of reminders:
  - Emails sent successfully
  - Template placeholders replaced correctly
  - Professional formatting maintained
  ```

### ✅ Performance Monitoring

- [ ] **Monitor Service Performance**
  ```
  Track metrics:
  - Execution time per service run
  - Memory usage during execution
  - Database query performance
  - Email sending throughput
  ```

- [ ] **Set Up Alerting**
  ```
  Configure alerts for:
  - Service execution failures
  - High email failure rates
  - Unusual execution times
  - Configuration errors
  ```

### ✅ Business Validation

- [ ] **Verify Business Logic**
  ```
  Confirm:
  - Correct students receive reminders
  - Lesson threshold working as expected
  - Payment deadline calculation accurate
  - Email content appropriate and professional
  ```

- [ ] **Stakeholder Approval**
  ```
  Get confirmation from:
  - Studio administrators
  - Finance team
  - Customer service team
  ```

## 📊 Monitoring Setup

### ✅ Log Monitoring

- [ ] **Configure Log Aggregation**
  ```json
  {
    "Serilog": {
      "WriteTo": [
        {
          "Name": "File",
          "Args": {
            "path": "logs/payment-reminder-.txt",
            "rollingInterval": "Day",
            "retainedFileCountLimit": 30
          }
        }
      ]
    }
  }
  ```

- [ ] **Set Up Log Alerts**
  ```
  Monitor for:
  - "Error occurred during payment reminder processing"
  - "Failed to send payment reminder"
  - Service startup/shutdown events
  ```

### ✅ Performance Metrics

- [ ] **Database Performance**
  ```sql
  -- Monitor query performance
  SELECT 
    execution_count,
    total_elapsed_time,
    avg_elapsed_time = total_elapsed_time / execution_count,
    query_text = SUBSTRING(st.text, (qs.statement_start_offset/2)+1, 
                          ((CASE qs.statement_end_offset 
                            WHEN -1 THEN DATALENGTH(st.text)
                            ELSE qs.statement_end_offset 
                            END - qs.statement_start_offset)/2) + 1)
  FROM sys.dm_exec_query_stats qs
  CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) st
  WHERE st.text LIKE '%PaymentReminder%'
  ORDER BY avg_elapsed_time DESC
  ```

- [ ] **Email Service Metrics**
  ```
  Track:
  - Email send success rate
  - Email delivery time
  - SMTP connection health
  - Bounce/failure rates
  ```

## 🚨 Rollback Plan

### ✅ Emergency Rollback Procedures

- [ ] **Disable Service Temporarily**
  ```bash
  # Set very high interval to effectively disable
  export PAYMENT_REMINDER_INTERVAL_HOURS=87600  # 10 years
  # Restart application
  ```

- [ ] **Database Rollback (if needed)**
  ```sql
  -- Remove ExcludeEmail field (only if absolutely necessary)
  ALTER TABLE Students DROP COLUMN ExcludeEmail
  
  -- Remove email template
  DELETE FROM EmailTemplates WHERE Name = 'PaymentReminder'
  ```

- [ ] **Application Rollback**
  ```bash
  # Deploy previous version
  # Remove service registration from Program.cs
  # Restart application
  ```

## 📞 Support Contacts

### ✅ Escalation Path

- [ ] **Level 1: Application Logs**
  ```
  Check for service-specific log entries
  Verify configuration values
  Confirm database connectivity
  ```

- [ ] **Level 2: Database Administrator**
  ```
  Database performance issues
  Query optimization needs
  Schema-related problems
  ```

- [ ] **Level 3: System Administrator**
  ```
  Email service configuration
  Environment variable issues
  Application hosting problems
  ```

- [ ] **Level 4: Development Team**
  ```
  Code-related issues
  Logic errors
  Feature enhancement requests
  ```

## 📝 Documentation Updates

### ✅ Update Documentation

- [ ] **System Documentation**
  - Update architecture diagrams
  - Document new service dependencies
  - Update deployment procedures

- [ ] **User Documentation**
  - Update admin user guide
  - Document email preference settings
  - Update troubleshooting guides

- [ ] **Operational Documentation**
  - Update monitoring procedures
  - Document alerting thresholds
  - Update backup procedures

---

## ✅ Final Deployment Sign-off

**Deployment Date:** _______________  
**Deployed By:** _______________  
**Verified By:** _______________  

**Stakeholder Approvals:**
- [ ] Technical Lead: _______________
- [ ] Database Administrator: _______________
- [ ] System Administrator: _______________
- [ ] Business Owner: _______________

**Post-Deployment Notes:**
_________________________________
_________________________________
_________________________________

**Next Review Date:** _______________
