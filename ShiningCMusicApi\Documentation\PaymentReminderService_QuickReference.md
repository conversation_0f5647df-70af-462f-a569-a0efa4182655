# Payment Reminder Service - Quick Reference Guide

## 🚀 Quick Setup Checklist

### Database Setup
```bash
# 1. Add ExcludeEmail field to Students table
sqlcmd -S your-server -d MusicSchool -i "SQL/Add_ExcludeEmail_Field.sql"

# 2. Add PaymentReminder email template
sqlcmd -S your-server -d MusicSchool -i "SQL/Add_PaymentReminder_Template.sql"

# 3. Verify setup
sqlcmd -S your-server -d MusicSchool -i "SQL/Test_PaymentReminder_Setup.sql"
```

### Configuration (Choose One)

#### Option A: Environment Variables (Production)
```bash
export PAYMENT_REMINDER_INTERVAL_HOURS=24
export PAYMENT_REMINDER_LESSON_THRESHOLD=3
export PAYMENT_REMINDER_DEADLINE_DAYS=7
```

#### Option B: appsettings.json (Development)
```json
{
  "PaymentReminder": {
    "IntervalHours": 24,
    "LessonThreshold": 3,
    "PaymentDeadlineDays": 7
  }
}
```

## ⚙️ Configuration Parameters

| Setting | Description | Default | Recommended Range |
|---------|-------------|---------|-------------------|
| **IntervalHours** | How often service runs | 24 | 12-48 hours |
| **LessonThreshold** | Lessons remaining to trigger reminder | 3 | 2-5 lessons |
| **PaymentDeadlineDays** | Days from now for payment deadline | 7 | 5-14 days |

## 📧 Email Management

### Student Email Preferences
```sql
-- Exclude student from payment reminders
UPDATE Students SET ExcludeEmail = 1 WHERE StudentId = @StudentId

-- Include student in payment reminders
UPDATE Students SET ExcludeEmail = 0 WHERE StudentId = @StudentId

-- Check current email settings
SELECT StudentId, StudentName, Email, ExcludeEmail 
FROM Students 
WHERE IsArchived = 0
ORDER BY StudentName
```

### Email Template Placeholders
- `{StudentName}` → Student's full name
- `{LessonsRemaining}` → Number of lessons left
- `{PaymentDeadline}` → Calculated payment deadline date

## 🔍 Monitoring & Diagnostics

### Check Service Status
Look for these log entries:
```
✅ PaymentReminderService started.
✅ Successfully sent X payment reminder(s).
ℹ️  No payment reminders needed at this time.
❌ Error occurred during payment reminder processing.
```

### Quick Diagnostic Queries

#### Who Will Receive Reminders?
```sql
SELECT 
    s.StudentId,
    s.StudentName,
    s.Email,
    COUNT(l.LessonId) as FutureLessons
FROM Students s
LEFT JOIN Lessons l ON s.StudentId = l.StudentId 
    AND l.IsArchived = 0 
    AND l.StartTime > GETDATE()
    AND l.IsRecurring = 0  -- Simple count for quick check
WHERE s.IsArchived = 0 
    AND s.ExcludeEmail = 0 
    AND s.Email IS NOT NULL 
    AND s.Email != ''
GROUP BY s.StudentId, s.StudentName, s.Email
HAVING COUNT(l.LessonId) <= 3 AND COUNT(l.LessonId) > 0  -- Adjust threshold as needed
ORDER BY COUNT(l.LessonId), s.StudentName
```

#### Check Email Template
```sql
SELECT Name, Subject, 
       CASE WHEN BodyHtml IS NOT NULL THEN 'HTML Available' ELSE 'Text Only' END as Format
FROM EmailTemplates 
WHERE Name = 'PaymentReminder'
```

#### Student Email Settings Summary
```sql
SELECT 
    COUNT(*) as TotalStudents,
    SUM(CASE WHEN ExcludeEmail = 1 THEN 1 ELSE 0 END) as ExcludedFromEmail,
    SUM(CASE WHEN Email IS NULL OR Email = '' THEN 1 ELSE 0 END) as NoEmailAddress,
    SUM(CASE WHEN IsArchived = 1 THEN 1 ELSE 0 END) as ArchivedStudents
FROM Students
```

## 🛠️ Common Administrative Tasks

### Temporarily Disable Service
```json
// Set very high interval in appsettings.json
{
  "PaymentReminder": {
    "IntervalHours": 8760  // Once per year
  }
}
```

### Change Reminder Frequency
```bash
# Daily reminders
export PAYMENT_REMINDER_INTERVAL_HOURS=24

# Twice daily
export PAYMENT_REMINDER_INTERVAL_HOURS=12

# Weekly
export PAYMENT_REMINDER_INTERVAL_HOURS=168
```

### Adjust Sensitivity
```bash
# More sensitive (remind earlier)
export PAYMENT_REMINDER_LESSON_THRESHOLD=5

# Less sensitive (remind later)
export PAYMENT_REMINDER_LESSON_THRESHOLD=2
```

### Bulk Email Exclusion
```sql
-- Exclude all students in a specific subject
UPDATE Students 
SET ExcludeEmail = 1 
WHERE SubjectId = @SubjectId

-- Exclude students without recent lessons
UPDATE Students 
SET ExcludeEmail = 1 
WHERE StudentId NOT IN (
    SELECT DISTINCT StudentId 
    FROM Lessons 
    WHERE StartTime > DATEADD(month, -3, GETDATE())
    AND IsArchived = 0
)
```

## 🚨 Troubleshooting Quick Fixes

### Issue: No Reminders Being Sent

**Check 1: Service Running?**
```
Look for "PaymentReminderService started" in logs
```

**Check 2: Students Eligible?**
```sql
-- Should return students with low lesson counts
SELECT COUNT(*) as EligibleStudents
FROM Students s
WHERE s.IsArchived = 0 
    AND s.ExcludeEmail = 0 
    AND s.Email IS NOT NULL 
    AND s.Email != ''
```

**Check 3: Email Template Exists?**
```sql
-- Should return 1 row
SELECT COUNT(*) FROM EmailTemplates WHERE Name = 'PaymentReminder'
```

### Issue: Wrong Lesson Counts

**Check Lesson Data:**
```sql
SELECT 
    StudentId,
    StartTime,
    IsRecurring,
    RecurrenceRule,
    IsArchived
FROM Lessons 
WHERE StudentId = @StudentId  -- Replace with problem student
    AND StartTime > GETDATE()
ORDER BY StartTime
```

### Issue: Email Delivery Failures

**Check Email Configuration:**
```json
// Verify in appsettings.json
{
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "SenderEmail": "<EMAIL>",
    "SenderPassword": "your-app-password"
  }
}
```

## 📊 Performance Monitoring

### Key Metrics to Track
- **Execution Time**: How long each service run takes
- **Reminder Volume**: Number of reminders sent per day/week
- **Success Rate**: Percentage of successful email deliveries
- **Student Coverage**: Percentage of eligible students processed

### Log Analysis Queries
```bash
# Count service executions (search application logs)
grep "PaymentReminderService started" application.log | wc -l

# Count successful reminders (search application logs)
grep "Successfully sent.*payment reminder" application.log

# Find errors (search application logs)
grep "Error.*payment reminder" application.log
```

## 🔧 Advanced Configuration

### Custom Email Template
```sql
-- Update email subject
UPDATE EmailTemplates 
SET Subject = 'Custom Subject - {LessonsRemaining} Lessons Left'
WHERE Name = 'PaymentReminder'

-- Update email content (be careful with HTML formatting)
UPDATE EmailTemplates 
SET BodyHtml = '<!-- Your custom HTML content with placeholders -->'
WHERE Name = 'PaymentReminder'
```

### Environment-Specific Settings

#### Development
```json
{
  "PaymentReminder": {
    "IntervalHours": 1,      // Test every hour
    "LessonThreshold": 10,   // Higher threshold for testing
    "PaymentDeadlineDays": 3 // Shorter deadline for testing
  }
}
```

#### Production
```bash
export PAYMENT_REMINDER_INTERVAL_HOURS=24
export PAYMENT_REMINDER_LESSON_THRESHOLD=3
export PAYMENT_REMINDER_DEADLINE_DAYS=7
```

## 📞 Support Contacts

For technical issues:
1. Check application logs first
2. Run diagnostic queries
3. Verify configuration settings
4. Contact system administrator with specific error messages

---

**Last Updated:** [Current Date]  
**Version:** 1.0  
**Compatibility:** Shining C Music Studio API v1.0+
