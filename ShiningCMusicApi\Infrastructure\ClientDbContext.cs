﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Reflection.Emit;

namespace ShiningCMusicApi.Infrastructure
{
    public class ClientDbContext : DbContext
    {
        public ClientDbContext(DbContextOptions<ClientDbContext> options)
            : base(options)
        {
        }

        // Legacy IdentityServer4 entities (keep for migration)
        public DbSet<ClientEntity> Clients { get; set; }
        public DbSet<ClientScopeEntity> ClientScopes { get; set; }
        public DbSet<ClientGrantTypeEntity> ClientGrantTypes { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ClientEntity>()
                .<PERSON><PERSON><PERSON>(c => c.ClientId);

            modelBuilder.Entity<ClientEntity>()
                .Property(c => c.ClientSecret)
                .IsRequired();

            modelBuilder.Entity<ClientScopeEntity>()
                .<PERSON><PERSON><PERSON>(cs => cs.Id);

            modelBuilder.Entity<ClientScopeEntity>()
                .HasOne(cs => cs.Client)
                .WithMany(c => c.AllowedScopes)
                .HasForeignKey(cs => cs.ClientId);

            modelBuilder.Entity<ClientGrantTypeEntity>()
                .HasKey(cg => cg.Id);

            modelBuilder.Entity<ClientGrantTypeEntity>()
                .HasOne(cg => cg.Client)
                .WithMany(c => c.AllowedGrantTypes)
                .HasForeignKey(cg => cg.ClientId);
        }
    }
}
