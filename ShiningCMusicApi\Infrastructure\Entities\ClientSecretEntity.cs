using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicApi.Infrastructure.Entities
{
    /// <summary>
    /// Entity for storing plain text client secrets for OpenIddict clients
    /// This is separate from OpenIddict's hashed secrets for management purposes
    /// </summary>
    public class ClientSecretEntity
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string ClientId { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string PlainTextSecret { get; set; } = string.Empty;

        public int AccessTokenLifetimeSeconds { get; set; } = 3600; // Default 1 hour

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        [MaxLength(200)]
        public string? Description { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;
    }
}
