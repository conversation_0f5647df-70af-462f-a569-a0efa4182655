-- Add Visible column to Config table
-- This script adds a Visible column to control which config items appear in the settings UI

BEGIN TRANSACTION;

-- Add the Visible column if it doesn't exist
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Config' AND COLUMN_NAME = 'Visible')
BEGIN
    ALTER TABLE [dbo].[Config] 
    ADD [Visible] BIT NOT NULL DEFAULT 1;
    
    PRINT 'Added Visible column to Config table';
END
ELSE
BEGIN
    PRINT 'Visible column already exists in Config table';
END
GO

-- Set CustomSidebarColor1 and CustomSidebarColor2 to not visible
UPDATE [dbo].[Config] 
SET [Visible] = 0 
WHERE [Key] IN ('CustomSidebarColor1', 'CustomSidebarColor2');

PRINT 'Updated CustomSidebarColor configs to not visible';

COMMIT TRANSACTION;

-- Verify the changes
SELECT [ConfigId], [GroupId], [Key], [Value], [Visible], [Description]
FROM [dbo].[Config]
WHERE [Key] IN ('CustomSidebarColor1', 'CustomSidebarColor2', 'SidebarTheme')
ORDER BY [Key];

-- Show all UI configs to verify visibility settings
SELECT [ConfigId], [GroupId], [Key], [Value], [Visible], [Description]
FROM [dbo].[Config]
WHERE [GroupId] = 300
ORDER BY [Key];
