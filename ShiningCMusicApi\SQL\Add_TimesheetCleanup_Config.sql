USE [MusicSchool]
GO

-- Add TimesheetCleanup configuration entries to Config table
-- Background Processor configurations (GroupId: 200)

-- Check if TimesheetCleanupEnabled already exists
IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'TimesheetCleanupEnabled')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
    VALUES (200, 'TimesheetCleanupEnabled', 'true', 'Enable/disable timesheet cleanup service', 'bool');
    PRINT 'Added TimesheetCleanupEnabled configuration';
END
ELSE
BEGIN
    PRINT 'TimesheetCleanupEnabled configuration already exists';
END

-- Check if TimesheetCleanupIntervalHours already exists
IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'TimesheetCleanupIntervalHours')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
    VALUES (200, 'TimesheetCleanupIntervalHours', '24', 'Timesheet cleanup interval in hours', 'int');
    PRINT 'Added TimesheetCleanupIntervalHours configuration';
END
ELSE
BEGIN
    PRINT 'TimesheetCleanupIntervalHours configuration already exists';
END

-- Check if TimesheetCleanupRetentionDays already exists
IF NOT EXISTS (SELECT 1 FROM [dbo].[Config] WHERE [GroupId] = 200 AND [Key] = 'TimesheetCleanupRetentionDays')
BEGIN
    INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
    VALUES (200, 'TimesheetCleanupRetentionDays', '90', 'Timesheet cleanup retention period in days', 'int');
    PRINT 'Added TimesheetCleanupRetentionDays configuration';
END
ELSE
BEGIN
    PRINT 'TimesheetCleanupRetentionDays configuration already exists';
END

-- Verify the configurations were added
SELECT 
    [ConfigId],
    [GroupId],
    [Key],
    [Value],
    [Description],
    [DataType],
    [CreatedUTC]
FROM [dbo].[Config] 
WHERE [GroupId] = 200 
AND [Key] LIKE 'TimesheetCleanup%'
ORDER BY [Key];

PRINT 'TimesheetCleanup configuration setup completed successfully';
GO
