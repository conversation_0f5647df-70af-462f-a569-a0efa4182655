using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class TimesheetCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TimesheetCleanupService> _logger;
        private readonly IConfiguration _configuration;
        private TimeSpan _period;
        private int _retentionDays;
        private bool _isEnabled;

        public TimesheetCleanupService(
            IServiceProvider serviceProvider,
            ILogger<TimesheetCleanupService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;

            // Initialize with default values - will be updated from database during execution
            _period = TimeSpan.FromHours(24);
            _retentionDays = 90; // Default to 90 days for timesheets
            _isEnabled = true;

            _logger.LogInformation("TimesheetCleanupService initialized.");
        }

        private async Task<bool> LoadConfigurationAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var configService = scope.ServiceProvider.GetRequiredService<IConfigService>();

                // Check if service is enabled
                _isEnabled = await configService.IsBackgroundProcessorEnabledAsync("TimesheetCleanup");
                if (!_isEnabled)
                {
                    _logger.LogInformation("TimesheetCleanupService is disabled in configuration.");
                    return false;
                }

                // Load configuration values with fallback to environment variables and appsettings.json
                var intervalHours = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "TimesheetCleanupIntervalHours",
                    "TIMESHEET_CLEANUP_INTERVAL_HOURS", "TimesheetCleanup:IntervalHours", 24);
                _retentionDays = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "TimesheetCleanupRetentionDays",
                    "TIMESHEET_CLEANUP_RETENTION_DAYS", "TimesheetCleanup:RetentionDays", 90);

                _period = TimeSpan.FromHours(intervalHours);

                _logger.LogInformation("TimesheetCleanupService configuration loaded: Interval={Hours}h, Retention={Days}d, Enabled={Enabled}",
                    intervalHours, _retentionDays, _isEnabled);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load configuration from database, using fallback values.");

                // Fallback to original configuration method
                var intervalHours = GetConfigurationValue("TIMESHEET_CLEANUP_INTERVAL_HOURS", "TimesheetCleanup:IntervalHours", 24);
                _retentionDays = GetConfigurationValue("TIMESHEET_CLEANUP_RETENTION_DAYS", "TimesheetCleanup:RetentionDays", 90);
                _period = TimeSpan.FromHours(intervalHours);
                _isEnabled = true;
                return true;
            }
        }

        private async Task<int> GetConfigValueWithFallbackAsync(IConfigService configService, int groupId, string key,
            string envVar, string configKey, int defaultValue)
        {
            // Try database first
            var dbValue = await configService.GetConfigValueAsync<int?>(groupId, key);
            if (dbValue.HasValue)
            {
                _logger.LogInformation("Using database config {GroupId}:{Key} = {Value}", groupId, key, dbValue.Value);
                return dbValue.Value;
            }

            // Fall back to environment variable
            var envValue = Environment.GetEnvironmentVariable(envVar);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", envVar, envIntValue);
                return envIntValue;
            }

            // Fall back to appsettings.json
            var configValue = _configuration.GetValue<int>(configKey, defaultValue);
            _logger.LogInformation("Using appsettings.json {ConfigKey} = {Value} (default: {Default})",
                configKey, configValue, defaultValue);
            return configValue;
        }

        private int GetConfigurationValue(string environmentVariableName, string configurationKey, int defaultValue)
        {
            // First try environment variable
            var envValue = Environment.GetEnvironmentVariable(environmentVariableName);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", environmentVariableName, envIntValue);
                return envIntValue;
            }

            // Fall back to configuration
            var configValue = _configuration.GetValue<int>(configurationKey, defaultValue);
            _logger.LogInformation("Using configuration {ConfigKey} = {Value} (default: {Default})",
                configurationKey, configValue, defaultValue);
            return configValue;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("TimesheetCleanupService started.");

            // Wait for initial delay to avoid running immediately on startup
            await Task.Delay(TimeSpan.FromMinutes(3), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Load configuration from database before each execution
                    var configLoaded = await LoadConfigurationAsync();
                    if (configLoaded && _isEnabled)
                    {
                        await PerformCleanupAsync();
                    }
                    else if (!_isEnabled)
                    {
                        _logger.LogDebug("TimesheetCleanupService is disabled, skipping cleanup.");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during timesheet cleanup.");
                }

                try
                {
                    await Task.Delay(_period, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("TimesheetCleanupService stopped.");
        }

        private async Task PerformCleanupAsync()
        {
            _logger.LogInformation("Starting timesheet cleanup process...");

            using var scope = _serviceProvider.CreateScope();
            var timesheetService = scope.ServiceProvider.GetRequiredService<ITimesheetService>();

            try
            {
                // Get counts before deletion for logging
                var timesheetsToDelete = await timesheetService.GetArchivedTimesheetsCountAsync(_retentionDays);
                var entriesToDelete = await timesheetService.GetArchivedTimesheetEntriesCountAsync(_retentionDays);

                if (timesheetsToDelete > 0)
                {
                    _logger.LogInformation("Found {TimesheetCount} archived timesheets and {EntryCount} timesheet entries older than {Days} days to delete.",
                        timesheetsToDelete, entriesToDelete, _retentionDays);

                    var deletedCount = await timesheetService.PermanentlyDeleteArchivedTimesheetsAsync(_retentionDays);

                    _logger.LogInformation("Successfully deleted {TimesheetCount} archived timesheets and {EntryCount} timesheet entries older than {Days} days.",
                        deletedCount, entriesToDelete, _retentionDays);
                }
                else
                {
                    _logger.LogInformation("No archived timesheets older than {Days} days found for cleanup.", _retentionDays);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform timesheet cleanup.");
                throw;
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("TimesheetCleanupService is stopping.");
            await base.StopAsync(stoppingToken);
        }
    }
}
