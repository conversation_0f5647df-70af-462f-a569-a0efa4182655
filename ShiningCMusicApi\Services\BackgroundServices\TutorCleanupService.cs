using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.BackgroundServices
{
    public class TutorCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TutorCleanupService> _logger;
        private readonly IConfiguration _configuration;
        private TimeSpan _period;
        private int _retentionDays;
        private bool _isEnabled;

        public TutorCleanupService(
            IServiceProvider serviceProvider,
            ILogger<TutorCleanupService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;

            // Initialize with default values - will be updated from database during execution
            _period = TimeSpan.FromHours(24);
            _retentionDays = 30;
            _isEnabled = true;

            _logger.LogInformation("TutorCleanupService initialized.");
        }

        private async Task<bool> LoadConfigurationAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var configService = scope.ServiceProvider.GetRequiredService<IConfigService>();

                // Check if service is enabled
                _isEnabled = await configService.IsBackgroundProcessorEnabledAsync("TutorCleanup");
                if (!_isEnabled)
                {
                    _logger.LogInformation("TutorCleanupService is disabled in configuration.");
                    return false;
                }

                // Load configuration values with fallback to environment variables and appsettings.json
                var intervalHours = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "TutorCleanupIntervalHours",
                    "TUTOR_CLEANUP_INTERVAL_HOURS", "TutorCleanup:IntervalHours", 24);
                _retentionDays = await GetConfigValueWithFallbackAsync(configService, (int)ConfigGroupId.BackgroundProcessors, "TutorCleanupRetentionDays",
                    "TUTOR_CLEANUP_RETENTION_DAYS", "TutorCleanup:RetentionDays", 30);

                _period = TimeSpan.FromHours(intervalHours);

                _logger.LogInformation("TutorCleanupService configuration loaded: Interval={Hours}h, Retention={Days}d, Enabled={Enabled}",
                    intervalHours, _retentionDays, _isEnabled);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to load configuration from database, using fallback values.");

                // Fallback to original configuration method
                var intervalHours = GetConfigurationValue("TUTOR_CLEANUP_INTERVAL_HOURS", "TutorCleanup:IntervalHours", 24);
                _retentionDays = GetConfigurationValue("TUTOR_CLEANUP_RETENTION_DAYS", "TutorCleanup:RetentionDays", 30);
                _period = TimeSpan.FromHours(intervalHours);
                _isEnabled = true;
                return true;
            }
        }

        private async Task<int> GetConfigValueWithFallbackAsync(IConfigService configService, int groupId, string key,
            string envVar, string configKey, int defaultValue)
        {
            // Try database first
            var dbValue = await configService.GetConfigValueAsync<int?>(groupId, key);
            if (dbValue.HasValue)
            {
                _logger.LogInformation("Using database config {GroupId}:{Key} = {Value}", groupId, key, dbValue.Value);
                return dbValue.Value;
            }

            // Fall back to environment variable
            var envValue = Environment.GetEnvironmentVariable(envVar);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", envVar, envIntValue);
                return envIntValue;
            }

            // Fall back to appsettings.json
            var configValue = _configuration.GetValue<int>(configKey, defaultValue);
            _logger.LogInformation("Using appsettings.json {ConfigKey} = {Value} (default: {Default})",
                configKey, configValue, defaultValue);
            return configValue;
        }

        private int GetConfigurationValue(string environmentVariableName, string configurationKey, int defaultValue)
        {
            // First try environment variable
            var envValue = Environment.GetEnvironmentVariable(environmentVariableName);
            if (!string.IsNullOrEmpty(envValue) && int.TryParse(envValue, out var envIntValue))
            {
                _logger.LogInformation("Using environment variable {EnvVar} = {Value}", environmentVariableName, envIntValue);
                return envIntValue;
            }

            // Fall back to configuration
            var configValue = _configuration.GetValue<int>(configurationKey, defaultValue);
            _logger.LogInformation("Using configuration {ConfigKey} = {Value} (default: {Default})",
                configurationKey, configValue, defaultValue);
            return configValue;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("TutorCleanupService started.");

            // Wait for initial delay to avoid running immediately on startup
            await Task.Delay(TimeSpan.FromMinutes(2), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Load configuration from database before each execution
                    var configLoaded = await LoadConfigurationAsync();
                    if (configLoaded && _isEnabled)
                    {
                        await PerformCleanupAsync();
                    }
                    else if (!_isEnabled)
                    {
                        _logger.LogDebug("TutorCleanupService is disabled, skipping cleanup.");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred during tutor cleanup.");
                }

                try
                {
                    await Task.Delay(_period, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
            }

            _logger.LogInformation("TutorCleanupService stopped.");
        }

        private async Task PerformCleanupAsync()
        {
            _logger.LogInformation("Starting tutor cleanup process...");

            using var scope = _serviceProvider.CreateScope();
            var tutorService = scope.ServiceProvider.GetRequiredService<ITutorService>();

            try
            {
                var deletedCount = await tutorService.PermanentlyDeleteArchivedTutorsAsync(_retentionDays);

                if (deletedCount > 0)
                {
                    _logger.LogInformation("Successfully deleted {Count} archived tutors older than {Days} days.",
                        deletedCount, _retentionDays);
                }
                else
                {
                    _logger.LogInformation("No archived tutors older than {Days} days found for cleanup.", _retentionDays);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to perform tutor cleanup.");
                throw;
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("TutorCleanupService is stopping.");
            await base.StopAsync(stoppingToken);
        }
    }
}
