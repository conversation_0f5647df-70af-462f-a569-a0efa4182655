using OpenIddict.Abstractions;
using OpenIddict.Server;
using ShiningCMusicApi.Services.Interfaces;
using System.Security.Claims;
using static OpenIddict.Server.OpenIddictServerEvents;

namespace ShiningCMusicApi.Services
{
    public class CustomTokenLifetimeHandler : IOpenIddictServerHandler<HandleTokenRequestContext>
    {
        private readonly IClientSecretService _clientSecretService;

        public CustomTokenLifetimeHandler(IClientSecretService clientSecretService)
        {
            _clientSecretService = clientSecretService;
        }

        public async ValueTask HandleAsync(HandleTokenRequestContext context)
        {
            // Only handle client credentials flow
            if (context.Request.IsClientCredentialsGrantType())
            {
                // Create a new ClaimsIdentity containing the claims that will be used to create tokens
                var identity = new ClaimsIdentity(
                    authenticationType: "Bearer",
                    nameType: OpenIddictConstants.Claims.Name,
                    roleType: OpenIddictConstants.Claims.Role);

                // Use the client_id as the subject identifier
                identity.SetClaim(OpenIddictConstants.Claims.Subject, context.Request.ClientId ?? throw new InvalidOperationException());
                identity.SetClaim(OpenIddictConstants.Claims.Name, context.Request.ClientId ?? throw new InvalidOperationException());

                identity.SetScopes(context.Request.GetScopes());
                identity.SetResources(new[] { "ShiningCMusicApi" });
                identity.SetDestinations(GetDestinations);

                var principal = new ClaimsPrincipal(identity);

                // Get custom token lifetime from database
                var clientId = context.Request.ClientId;
                if (!string.IsNullOrEmpty(clientId))
                {
                    var tokenLifetimeSeconds = await _clientSecretService.GetTokenLifetimeAsync(clientId);
                    principal.SetAccessTokenLifetime(TimeSpan.FromSeconds(tokenLifetimeSeconds));
                }

                context.Principal = principal;
            }
        }

        private static IEnumerable<string> GetDestinations(Claim claim)
        {
            // Note: by default, claims are NOT automatically included in the access and identity tokens.
            // To allow OpenIddict to serialize them, you must attach them a destination, that specifies
            // whether they should be included in access tokens, in identity tokens or in both.

            switch (claim.Type)
            {
                case OpenIddictConstants.Claims.Name:
                    yield return OpenIddictConstants.Destinations.AccessToken;
                    yield break;

                case OpenIddictConstants.Claims.Email:
                    yield return OpenIddictConstants.Destinations.AccessToken;
                    yield break;

                case OpenIddictConstants.Claims.Role:
                    yield return OpenIddictConstants.Destinations.AccessToken;
                    yield break;

                default:
                    yield return OpenIddictConstants.Destinations.AccessToken;
                    yield break;
            }
        }
    }
}
