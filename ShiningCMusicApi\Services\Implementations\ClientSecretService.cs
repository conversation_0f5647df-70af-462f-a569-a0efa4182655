using Microsoft.EntityFrameworkCore;
using ShiningCMusicApi.Infrastructure;
using ShiningCMusicApi.Infrastructure.Entities;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.Implementations
{
    public class ClientSecretService : IClientSecretService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ClientSecretService> _logger;

        public ClientSecretService(ApplicationDbContext context, ILogger<ClientSecretService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<string?> GetPlainTextSecretAsync(string clientId)
        {
            var clientSecret = await _context.ClientSecrets
                .FirstOrDefaultAsync(cs => cs.ClientId == clientId && cs.IsActive);
            
            return clientSecret?.PlainTextSecret;
        }

        public async Task<ClientSecretEntity?> GetClientSecretAsync(string clientId)
        {
            return await _context.ClientSecrets
                .FirstOrDefaultAsync(cs => cs.ClientId == clientId && cs.IsActive);
        }

        public async Task<int> GetTokenLifetimeAsync(string clientId)
        {
            var clientSecret = await _context.ClientSecrets
                .FirstOrDefaultAsync(cs => cs.ClientId == clientId && cs.IsActive);

            return clientSecret?.AccessTokenLifetimeSeconds ?? 7200; // Default 2 hours
        }

        public async Task<ClientSecretEntity> CreateClientSecretAsync(string clientId, string plainTextSecret, string? description = null, int tokenLifetimeSeconds = 3600)
        {
            // Check if client secret already exists
            var existing = await _context.ClientSecrets
                .FirstOrDefaultAsync(cs => cs.ClientId == clientId);

            if (existing != null)
            {
                throw new InvalidOperationException($"Client secret for '{clientId}' already exists. Use UpdateClientSecretAsync to modify it.");
            }

            var clientSecret = new ClientSecretEntity
            {
                ClientId = clientId,
                PlainTextSecret = plainTextSecret,
                Description = description,
                AccessTokenLifetimeSeconds = tokenLifetimeSeconds,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _context.ClientSecrets.Add(clientSecret);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created client secret for {ClientId}", clientId);
            return clientSecret;
        }

        public async Task<ClientSecretEntity> UpdateClientSecretAsync(string clientId, string newPlainTextSecret, int? tokenLifetimeSeconds = null)
        {
            var clientSecret = await _context.ClientSecrets
                .FirstOrDefaultAsync(cs => cs.ClientId == clientId && cs.IsActive);

            if (clientSecret == null)
            {
                throw new InvalidOperationException($"Client secret for '{clientId}' not found.");
            }

            clientSecret.PlainTextSecret = newPlainTextSecret;
            if (tokenLifetimeSeconds.HasValue)
            {
                clientSecret.AccessTokenLifetimeSeconds = tokenLifetimeSeconds.Value;
            }
            clientSecret.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Updated client secret for {ClientId}", clientId);
            return clientSecret;
        }

        public async Task<bool> DeleteClientSecretAsync(string clientId)
        {
            var clientSecret = await _context.ClientSecrets
                .FirstOrDefaultAsync(cs => cs.ClientId == clientId && cs.IsActive);

            if (clientSecret == null)
            {
                return false;
            }

            clientSecret.IsActive = false;
            clientSecret.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Deactivated client secret for {ClientId}", clientId);
            return true;
        }

        public async Task<bool> ValidateClientSecretAsync(string clientId, string providedSecret)
        {
            var storedSecret = await GetPlainTextSecretAsync(clientId);
            return storedSecret != null && storedSecret == providedSecret;
        }

        public async Task<List<ClientSecretEntity>> GetAllClientSecretsAsync()
        {
            return await _context.ClientSecrets
                .Where(cs => cs.IsActive)
                .OrderBy(cs => cs.ClientId)
                .ToListAsync();
        }
    }
}
