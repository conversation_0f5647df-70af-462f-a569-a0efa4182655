using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface IConfigService
    {
        Task<IEnumerable<Config>> GetAllConfigsAsync();
        Task<IEnumerable<ConfigGroup>> GetConfigGroupsAsync();
        Task<ConfigGroup?> GetConfigGroupAsync(int groupId);
        Task<Config?> GetConfigAsync(int configId);
        Task<Config?> GetConfigAsync(int groupId, string key);
        Task<string?> GetConfigValueAsync(int groupId, string key);
        Task<T?> GetConfigValueAsync<T>(int groupId, string key, T? defaultValue = default);
        Task<bool> UpdateConfigAsync(int configId, string value);
        Task<bool> UpdateConfigAsync(int groupId, string key, string value);
        Task<Config> CreateConfigAsync(Config config);
        Task<bool> DeleteConfigAsync(int configId);
        Task<bool> IsBackgroundProcessorEnabledAsync(string processorName);
    }
}
