using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface ITimesheetService
    {
        Task<IEnumerable<Timesheet>> GetTimesheetsAsync();
        Task<IEnumerable<Timesheet>> GetTimesheetsByTutorAsync(int tutorId);
        Task<Timesheet?> GetTimesheetAsync(int id);
        Task<Timesheet> CreateTimesheetAsync(Timesheet timesheet);
        Task<bool> UpdateTimesheetAsync(int id, Timesheet timesheet);
        Task<bool> DeleteTimesheetAsync(int id);
        
        // TimesheetEntry operations
        Task<IEnumerable<TimesheetEntry>> GetTimesheetEntriesAsync(int timesheetId);
        Task<TimesheetEntry?> GetTimesheetEntryAsync(int id);
        Task<TimesheetEntry> CreateTimesheetEntryAsync(TimesheetEntry entry);
        Task<IEnumerable<TimesheetEntry>> CreateMultipleTimesheetEntriesAsync(int timesheetId, int numberOfRecords);
        Task<bool> UpdateTimesheetEntryAsync(int id, TimesheetEntry entry);
        Task<bool> DeleteTimesheetEntryAsync(int id);

        // Cleanup operations
        Task<int> PermanentlyDeleteArchivedTimesheetsAsync(int olderThanDays);
        Task<int> GetArchivedTimesheetsCountAsync(int olderThanDays);
        Task<int> GetArchivedTimesheetEntriesCountAsync(int olderThanDays);
    }
}
