using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Services.Interfaces;

namespace ShiningCMusicApp.Components
{
    public partial class ThemeLoader : ComponentBase
    {
        [Parameter] public RenderFragment? ChildContent { get; set; }
        [Parameter] public string LoadingMessage { get; set; } = "Loading your application...";
        
        [Inject] private ISidebarThemeService SidebarThemeService { get; set; } = default!;
        [Inject] private ILogger<ThemeLoader> Logger { get; set; } = default!;
        
        private bool isThemeLoaded = false;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                Logger.LogInformation("ThemeLoader: Starting theme initialization");
                await LoadAndApplyThemeAsync();
                isThemeLoaded = true;
                Logger.LogInformation("ThemeLoader: Theme loaded successfully");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to load theme, proceeding with default");
                // Apply default theme and continue
                await ApplyDefaultThemeAsync();
                isThemeLoaded = true;
            }

            StateHasChanged();
        }

        private async Task LoadAndApplyThemeAsync()
        {
            Logger.LogInformation("ThemeLoader: Initializing sidebar theme service");
            await SidebarThemeService.InitializeAsync();
            Logger.LogInformation("ThemeLoader: Sidebar theme service initialized successfully");
        }

        private async Task ApplyDefaultThemeAsync()
        {
            try
            {
                Logger.LogInformation("ThemeLoader: Applying default theme");
                await SidebarThemeService.ApplyThemeAsync(SidebarTheme.BluePurple);
                Logger.LogInformation("Default theme applied");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to apply default theme");
            }
        }
    }
}
