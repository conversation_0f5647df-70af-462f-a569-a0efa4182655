.theme-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.theme-loading-content {
    text-align: center;
    color: #333;
    animation: fadeIn 0.8s ease-in-out;
}

.logo-container {
    margin-bottom: 2rem;
}

.loading-logo {
    width: 500px;
    height: auto;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    animation: logoGlow 2s ease-in-out infinite alternate;
}

.custom-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-top: 4px solid #ffd700;
    border-right: 4px solid #ff6b6b;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
    margin: 0 auto 1.5rem auto;
}

.app-title {
    margin: 1rem 0 0.5rem 0;
    font-weight: 600;
    font-size: 1.8rem;
    background: linear-gradient(45deg, #ffd700, #ff6b6b, #4ecdc4);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

.loading-text {
    margin: 0;
    opacity: 0.8;
    font-size: 1rem;
    color: #666;
    font-weight: 300;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
}

@keyframes logoGlow {
    0% { filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2)); }
    100% { filter: drop-shadow(0 4px 16px rgba(255, 215, 0, 0.3)); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .loading-logo {
        width: 150px;
    }

    .app-title {
        font-size: 1.5rem;
    }

    .loading-text {
        font-size: 0.9rem;
    }
}
