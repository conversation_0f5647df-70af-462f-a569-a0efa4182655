.page {
    position: relative;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.sidebar {
    /*background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);*/
    background-image: var(--sidebar-gradient);
}

.top-row {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

/* Mobile styles - handled in app.css now */

.user-info {
    margin-left: auto;
    display: flex;
    align-items: center;
}

@media (max-width: 991.98px) {
    .page {
        flex-direction: column;
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 1000;
        background: white;
        border-bottom: 1px solid #dee2e6;
        height: 3.5rem;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }

    .content {
        padding-top: 1rem;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Offcanvas customization */
    .offcanvas {
        width: 280px;
    }

    .offcanvas-body {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        padding: 0;
    }

    .offcanvas-header {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        color: white;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .offcanvas-title {
        color: white;
        font-size: 1.1rem;
    }

    .btn-close {
        filter: invert(1);
    }
}

@media (min-width: 992px) {
    .page {
        flex-direction: row;
    }

    .sidebar {
        flex: 0 0 250px;
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 5;
        background: white;
        border-bottom: 1px solid #dee2e6;
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    /* Hide mobile menu button on desktop */
    .mobile-menu-btn {
        display: none !important;
    }
}

/* Custom icon for mobile menu sliders */
.bi-sliders-fill-mobile-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'%3E%3Crect x='0' y='2.5' width='9' height='2' rx='1'/%3E%3Ccircle cx='11.5' cy='3.5' r='2' fill='white'/%3E%3Crect x='14' y='2.5' width='2' height='2' rx='1'/%3E%3Crect x='0' y='7.5' width='2' height='2' rx='1'/%3E%3Ccircle cx='4.5' cy='8.5' r='2' fill='white'/%3E%3Crect x='7' y='7.5' width='9' height='2' rx='1'/%3E%3Crect x='0' y='12.5' width='9' height='2' rx='1'/%3E%3Ccircle cx='11.5' cy='13.5' r='2' fill='white'/%3E%3Crect x='14' y='12.5' width='2' height='2' rx='1'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px 16px;
    display: inline-block;
    width: 16px;
    height: 16px;
}
