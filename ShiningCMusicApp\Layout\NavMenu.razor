﻿@using ShiningCMusicCommon.Enums

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <i class="bi bi-music-note me-2"></i>
            <span class="d-none d-sm-inline">Shining C Music</span>
            <span class="d-sm-none">SCM</span>
        </a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Home
            </NavLink>
        </div>
        <AuthorizeView Roles="@UserRoleEnum.Administrator.ToString()">
            <Authorized>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="lessons">
                        <span class="bi bi-calendar-event-nav-menu" aria-hidden="true"></span> Lessons
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="timesheets">
                        <span class="bi bi-clipboard-fill-nav-menu" aria-hidden="true"></span> Timesheets
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="tutors">
                        <span class="bi bi-person-fill-nav-menu" aria-hidden="true"></span> Tutors
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="students">
                        <span class="bi bi-people-fill-nav-menu" aria-hidden="true"></span> Students
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="email-templates">
                        <span class="bi bi-envelope-fill-nav-menu" aria-hidden="true"></span> Email Templates
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="admin">
                        <span class="bi bi-gear-fill-nav-menu" aria-hidden="true"></span> Maintenance
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="settings">
                        <span class="bi bi-sliders-fill-nav-menu" aria-hidden="true"></span> Settings
                    </NavLink>
                </div>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Roles="@($"{UserRoleEnum.Tutor},{UserRoleEnum.Student}")">
            <Authorized>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="lessons">
                        <span class="bi bi-calendar-event-nav-menu" aria-hidden="true"></span> My Lessons
                    </NavLink>
                </div>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Roles="@UserRoleEnum.Tutor.ToString()">
            <Authorized>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="timesheets">
                        <span class="bi bi-clipboard-fill-nav-menu" aria-hidden="true"></span> My Timesheets
                    </NavLink>
                </div>
            </Authorized>
        </AuthorizeView>
    </nav>
</div>
