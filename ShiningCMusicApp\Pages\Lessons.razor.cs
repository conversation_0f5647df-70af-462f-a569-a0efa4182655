using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Extensions;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;
using Syncfusion.Blazor.Schedule;
using System.Security.Claims;

namespace ShiningCMusicApp.Pages;

public partial class LessonsBase : ComponentBase, IDisposable
{
    [Inject] protected IBffDashboardService BffDashboard { get; set; } = default!;
    [Inject] protected IBffAdminService BffAdmin { get; set; } = default!;
    [Inject] protected BffAuthenticationStateProvider AuthStateProvider { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IDialogService DialogService { get; set; } = default!;

    // Data properties
    protected List<ScheduleEvent> scheduleEvents = new();
    protected List<ScheduleEvent> allScheduleEvents = new(); // Store all lessons for filtering
    protected List<Tutor> tutors = new();
    protected List<Student> students = new();
    protected List<Subject> subjects = new();
    protected List<Location> locations = new();
    protected DateTime selectedDate = DateTime.Today;
    protected View currentView = View.Week;
    protected bool isLoading = true;
    protected SfSchedule<ScheduleEvent>? scheduleRef;
    protected ScheduleEvent? currentEditingEvent;
    protected List<int> selectedTutorIds = new(); // Empty list means show all tutors

    // MediaQuery for responsive view switching
    protected string activeBreakpoint = "Large";

    // Computed property for desktop detection
    protected bool IsDesktop => activeBreakpoint == "Medium" || activeBreakpoint == "Large";

    // Syncfusion Recurrence Editor configuration
    protected List<RepeatType> AllowedFrequencies = new List<RepeatType>()
    {
        RepeatType.None,    // Never
        RepeatType.Daily,   // Daily
        RepeatType.Weekly,  // Weekly
        RepeatType.Monthly  // Monthly
    };

    protected List<EndType> AllowedEndTypes = new List<EndType>()
    {
        EndType.Never,  // Never ends
        EndType.Count,  // End after X occurrences
        EndType.Until   // End by specific date
    };

    // Track recurrence changes for auto-focus functionality
    protected string previousRecurrenceRule = string.Empty;

    // Role-based permissions
    protected AuthenticationState? authState;
    protected bool IsAdmin => authState?.User?.IsInRole(UserRoleEnum.Administrator) == true;
    protected bool CanAddEvents => IsAdmin;
    protected bool CanEditEvents => IsAdmin;
    protected bool CanDeleteEvents => IsAdmin;

    // Static reference for JavaScript interop
    protected static LessonsBase? _currentInstance;

    protected override async Task OnInitializedAsync()
    {
        _currentInstance = this;
        // Get authentication state first
        authState = await AuthStateProvider.GetAuthenticationStateAsync();
        await LoadData();
        await ProcessUrlParameters();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Set up JavaScript reference for file import
            await JSRuntime.InvokeVoidAsync("setLessonsPageReference", DotNetObjectReference.Create(this));

            // Initialize mobile QuickInfo popup fix
            await JSRuntime.InvokeVoidAsync("initMobileQuickInfoFix");

            // Set initial view based on current breakpoint after MediaQuery has initialized
            SetInitialView();
        }
    }

    protected async Task ProcessUrlParameters()
    {
        try
        {
            var uri = new Uri(Navigation.Uri);
            var queryString = uri.Query;

            // Check for studentId parameter
            if (queryString.Contains("studentId="))
            {
                var studentIdMatch = System.Text.RegularExpressions.Regex.Match(queryString, @"studentId=(\d+)");
                if (studentIdMatch.Success && int.TryParse(studentIdMatch.Groups[1].Value, out var studentId))
                {
                    await ApplyStudentFilter(studentId);
                    await JSRuntime.InvokeVoidAsync("console.log", $"Applied student filter: {studentId}");
                }
            }

            // Check for tutorId parameter
            if (queryString.Contains("tutorId="))
            {
                var tutorIdMatch = System.Text.RegularExpressions.Regex.Match(queryString, @"tutorId=(\d+)");
                if (tutorIdMatch.Success && int.TryParse(tutorIdMatch.Groups[1].Value, out var tutorId))
                {
                    await ApplyTutorFilterFromUrl(tutorId);
                    await JSRuntime.InvokeVoidAsync("console.log", $"Applied tutor filter: {tutorId}");
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error processing URL parameters: {ex.Message}");
        }
    }

    protected async Task ApplyStudentFilter(int studentId)
    {
        try
        {
            // Filter lessons for the specific student
            scheduleEvents = allScheduleEvents.Where(e => e.StudentId == studentId).ToList();

            // Refresh the schedule
            if (scheduleRef != null)
            {
                await scheduleRef.RefreshAsync();
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error applying student filter: {ex.Message}");
        }
    }

    protected async Task ApplyTutorFilterFromUrl(int tutorId)
    {
        try
        {
            // Set the selected tutor for filtering
            selectedTutorIds.Clear();
            selectedTutorIds.Add(tutorId);

            // Apply the filter
            ApplyTutorFilter();

            // Refresh the schedule
            if (scheduleRef != null)
            {
                await scheduleRef.RefreshAsync();
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error applying tutor filter from URL: {ex.Message}");
        }
    }

    protected async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load data from BFF Dashboard service (single aggregated call)
            var dashboardData = await BffDashboard.GetDashboardDataAsync();

            List<ScheduleEvent> allLessons;
            if (dashboardData != null)
            {
                allLessons = dashboardData.Lessons;
                tutors = dashboardData.Tutors;
                students = dashboardData.Students;
                subjects = dashboardData.Subjects;
                locations = dashboardData.Locations;
            }
            else
            {
                // Fallback to empty lists if dashboard data is null
                allLessons = new List<ScheduleEvent>();
                tutors = new List<Tutor>();
                students = new List<Student>();
                subjects = new List<Subject>();
                locations = new List<Location>();
            }

            // BFF already filters lessons based on user role, so we can use them directly
            allScheduleEvents = allLessons;
            scheduleEvents = allLessons;

            // Apply tutor filter if one is selected
            ApplyTutorFilter();

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {scheduleEvents.Count} lessons, {tutors.Count} tutors, {students.Count} students, {subjects.Count} subjects, {locations.Count} locations");

            // Debug: Log the actual lesson data and ensure proper DateTime format
            foreach (var lesson in scheduleEvents)
            {
                await JSRuntime.InvokeVoidAsync("console.log", $"Lesson: {lesson.Subject}, Start: {lesson.StartTime}, End: {lesson.EndTime}");

                // Ensure dates have DateTimeKind.Local
                lesson.StartTime = DateTime.SpecifyKind(lesson.StartTime, DateTimeKind.Local);
                lesson.EndTime = DateTime.SpecifyKind(lesson.EndTime, DateTimeKind.Local);

                // Assign color based on lesson status (trial/cancelled override tutor colors)
                if (lesson.IsCancelled)
                {
                    lesson.CategoryColor = "#6C757D"; // Gray for cancelled lessons
                }
                else if (lesson.IsTrial)
                {
                    lesson.CategoryColor = "#FFC107"; // Yellow for trial lessons (Bootstrap warning color)
                }
                else if (lesson.TutorId > 0)
                {
                    var tutor = tutors.FirstOrDefault(t => t.TutorId == lesson.TutorId);
                    lesson.CategoryColor = tutor?.Color ?? "#6C757D";
                }
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    // Handle recurrence value changes and auto-focus to section
    protected void OnRecurrenceValueChanged()
    {
        try
        {
            // Get the current value from the bound property
            string newValue = currentEditingEvent?.RecurrenceRule ?? string.Empty;

            // Parse the frequency from the RRULE
            string currentFrequency = ExtractFrequency(newValue);
            string previousFrequency = ExtractFrequency(previousRecurrenceRule);

            // Check if user switched from "None" to any frequency, or changed frequency types
            bool shouldScroll = false;

            if (string.IsNullOrEmpty(previousFrequency) && !string.IsNullOrEmpty(currentFrequency))
            {
                // User selected a frequency for the first time
                shouldScroll = true;
            }
            else if (!string.IsNullOrEmpty(previousFrequency) &&
                     !string.IsNullOrEmpty(currentFrequency) &&
                     previousFrequency != currentFrequency)
            {
                // User changed from one frequency to another
                shouldScroll = true;
            }

            if (shouldScroll)
            {
                // Use InvokeAsync to handle the async JavaScript call
                InvokeAsync(async () =>
                {
                    await Task.Delay(200); // Allow UI to update
                    await JSRuntime.InvokeVoidAsync("scrollToRecurrenceSection");
                });
            }

            // Update the previous value for next comparison
            previousRecurrenceRule = newValue;
        }
        catch (Exception ex)
        {
            InvokeAsync(async () =>
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnRecurrenceValueChanged: {ex.Message}");
            });
        }
    }

    // Extract frequency type from RRULE string
    protected string ExtractFrequency(string rrule)
    {
        if (string.IsNullOrEmpty(rrule)) return string.Empty;

        if (rrule.Contains("FREQ=DAILY")) return "DAILY";
        if (rrule.Contains("FREQ=WEEKLY")) return "WEEKLY";
        if (rrule.Contains("FREQ=MONTHLY")) return "MONTHLY";
        if (rrule.Contains("FREQ=YEARLY")) return "YEARLY";

        return string.Empty;
    }

    protected async Task AddNewLesson()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Add new lesson functionality will be implemented in the next step!");
    }

    protected async Task RefreshData()
    {
        await LoadData();
    }

    protected List<ScheduleEvent> GetScheduleEvents()
    {
        return scheduleEvents ?? new List<ScheduleEvent>();
    }

    protected void OnStudentChanged()
    {
        // This method will be called when student selection changes
        // Note: This method is called in the context of the editor template
        StateHasChanged();
    }

    protected void OnTutorChanged()
    {
        // This method will be called when tutor selection changes
        // Auto-assign color based on selected tutor
        // Note: This method is called in the context of the editor template
        // The actual color assignment will happen in the CRUD event handlers
        StateHasChanged();
    }

    protected void OnSubjectSelectionChanged()
    {
        // When subject changes, reset student and tutor selections
        if (currentEditingEvent != null)
        {
            currentEditingEvent.StudentId = 0;
            currentEditingEvent.TutorId = 0;
            currentEditingEvent.StudentName = null;
            currentEditingEvent.TutorName = null;
        }
        StateHasChanged();
    }

    protected List<Student> GetFilteredStudents()
    {
        if (currentEditingEvent?.SubjectId > 0)
        {
            var filtered = students.Where(s => s.SubjectId == currentEditingEvent.SubjectId).ToList();
            // Debug: Log the filtering
            JSRuntime.InvokeVoidAsync("console.log", $"Filtering students for SubjectId: {currentEditingEvent.SubjectId}");
            JSRuntime.InvokeVoidAsync("console.log", $"Total students: {students.Count}, Filtered students: {filtered.Count}");

            // If no students found for the subject, show all students as fallback
            if (filtered.Count == 0)
            {
                JSRuntime.InvokeVoidAsync("console.log", "No students found for subject, showing all students as fallback");
                return students.ToList();
            }

            return filtered;
        }
        return new List<Student>();
    }

    protected List<Tutor> GetFilteredTutors()
    {
        if (currentEditingEvent?.SubjectId > 0)
        {
            var filtered = tutors.Where(t => t.SubjectId == currentEditingEvent.SubjectId).ToList();
            // Debug: Log the filtering
            JSRuntime.InvokeVoidAsync("console.log", $"Filtering tutors for SubjectId: {currentEditingEvent.SubjectId}");
            JSRuntime.InvokeVoidAsync("console.log", $"Total tutors: {tutors.Count}, Filtered tutors: {filtered.Count}");
            foreach (var tutor in tutors)
            {
                JSRuntime.InvokeVoidAsync("console.log", $"Tutor: {tutor.TutorName}, SubjectId: {tutor.SubjectId}");
            }

            // If no tutors found for the subject, show all tutors as fallback
            if (filtered.Count == 0)
            {
                JSRuntime.InvokeVoidAsync("console.log", "No tutors found for subject, showing all tutors as fallback");
                return tutors.ToList();
            }

            return filtered;
        }
        return new List<Tutor>();
    }

    protected void OnLocationChanged()
    {
        // Force UI update when location changes
        StateHasChanged();
    }

    protected string GetTutorColor(int tutorId)
    {
        var tutor = tutors.FirstOrDefault(t => t.TutorId == tutorId);
        return tutor?.Color ?? "#6C757D"; // Default gray if not found
    }

    protected string GetTutorColorByName(string tutorName)
    {
        var tutor = tutors.FirstOrDefault(t => t.TutorName == tutorName);
        return tutor?.Color ?? "#6C757D";
    }

    protected async Task OnTutorColorChanged(int tutorId, string newColor)
    {
        // Update the tutor color in the database
        var success = await BffAdmin.UpdateTutorColorAsync(tutorId, newColor);

        if (success)
        {
            // Update the local tutor object
            var tutor = tutors.FirstOrDefault(t => t.TutorId == tutorId);
            if (tutor != null)
            {
                tutor.Color = newColor;
            }

            // Update all existing events for this tutor (but not trial or cancelled lessons)
            foreach (var scheduleEvent in scheduleEvents.Where(e => e.TutorId == tutorId))
            {
                // Only update color if not trial or cancelled (they have their own colors)
                if (!scheduleEvent.IsCancelled && !scheduleEvent.IsTrial)
                {
                    scheduleEvent.CategoryColor = newColor;
                }
            }

            // Refresh the schedule to show updated colors
            if (scheduleRef != null)
            {
                await scheduleRef.RefreshAsync();
            }

            StateHasChanged();

            await DialogService.ShowSuccessAsync("Color Change Successful", $"Color updated for all {tutor?.TutorName} lessons!");
        }
        else
        {
            await DialogService.ShowErrorAsync("Color Change Failed", "Failed to update tutor color. Please try again.");
        }
    }

    protected async Task OnTutorFilterChanged()
    {
        ApplyTutorFilter();

        // Refresh the schedule to show filtered events
        if (scheduleRef != null)
        {
            await scheduleRef.RefreshAsync();
        }

        StateHasChanged();
    }

    protected async Task OnTutorNameClicked(int tutorId)
    {
        // Toggle selection: if tutor is already selected, remove it; otherwise add it
        if (selectedTutorIds.Contains(tutorId))
        {
            selectedTutorIds.Remove(tutorId); // Remove from selection
        }
        else
        {
            selectedTutorIds.Add(tutorId); // Add to selection
        }

        ApplyTutorFilter();

        // Refresh the schedule to show filtered events
        if (scheduleRef != null)
        {
            await scheduleRef.RefreshAsync();
        }

        StateHasChanged();
    }

    protected async Task ClearTutorFilter()
    {
        selectedTutorIds.Clear();
        ApplyTutorFilter();

        // Refresh the schedule to show filtered events
        if (scheduleRef != null)
        {
            await scheduleRef.RefreshAsync();
        }

        StateHasChanged();
    }

    protected void ApplyTutorFilter()
    {
        if (!selectedTutorIds.Any())
        {
            // Show all lessons when no tutors are selected
            scheduleEvents = allScheduleEvents.ToList();
        }
        else
        {
            // Filter lessons for selected tutors
            scheduleEvents = allScheduleEvents.Where(e => selectedTutorIds.Contains(e.TutorId)).ToList();
        }
    }

    protected void OnEventRendered(EventRenderedArgs<ScheduleEvent> args)
    {
        // Apply the tutor color and tooltip to the event
        if (args.Data != null)
        {
            args.CssClasses = new List<string> { "custom-event" };

            // Create tooltip content
            var tooltipText = $"Tutor: {args.Data.TutorName ?? "Not assigned"}\nStudent: {args.Data.StudentName ?? "Not assigned"}\nTime: {args.Data.StartTime:h:mm tt} - {args.Data.EndTime:h:mm tt}";

            if (!string.IsNullOrEmpty(args.Data.Location))
            {
                tooltipText += $"\nLocation: {args.Data.Location}";
            }

            if (!string.IsNullOrEmpty(args.Data.Description))
            {
                tooltipText += $"\nDescription: {args.Data.Description}";
            }

            // Set attributes including color and tooltip
            args.Attributes = new Dictionary<string, object>
            {
                { "title", tooltipText },
                { "data-bs-toggle", "tooltip" },
                { "data-bs-placement", "top" }
            };

            // Apply tutor color if available
            if (!string.IsNullOrEmpty(args.Data.CategoryColor))
            {
                args.Attributes["style"] = $"background-color: {args.Data.CategoryColor} !important; border-color: {args.Data.CategoryColor} !important;";
            }
        }
    }

    protected async Task OnActionBegin(ActionEventArgs<ScheduleEvent> args)
    {
        try
        {
            // Remove mobile adaptive classes to prevent full-screen QuickInfo popup on mobile devices
            if (args.RequestType == RequestType.ToolbarItemRendering)
            {
                await JSRuntime.InvokeVoidAsync("removeMobileAdaptiveClasses");
            }

            // Check user permissions before allowing any action
            switch (args.ActionType)
            {
                case ActionType.EventCreate:
                    if (!CanAddEvents)
                    {
                        args.Cancel = true;
                        await DialogService.ShowWarningAsync("You don't have permission to create lessons.", "Please contact an administrator for access.");
                        return;
                    }
                    break;
                case ActionType.EventChange:
                    if (!CanEditEvents)
                    {
                        args.Cancel = true;
                        await DialogService.ShowWarningAsync("You don't have permission to edit lessons.", "Please contact an administrator for access.");
                        return;
                    }
                    // Let Syncfusion handle the UI changes, we'll process in OnActionComplete
                    break;
                case ActionType.EventRemove:
                    if (!CanDeleteEvents)
                    {
                        args.Cancel = true;
                        await DialogService.ShowWarningAsync("You don't have permission to delete lessons.", "Please contact an administrator for access.");
                        return;
                    }
                    // Let Syncfusion handle the UI changes, we'll process in OnActionComplete
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnActionBegin: {ex.Message}");
            args.Cancel = true;
            await DialogService.ShowErrorAsync("Error processing action", ex.Message);
        }
    }

    protected async Task OnActionComplete(ActionEventArgs<ScheduleEvent> args)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("console.log", $"OnActionComplete - ActionType: {args.ActionType}");

            switch (args.ActionType)
            {
                case ActionType.EventCreate:
                    await HandleCreateComplete(args);
                    break;
                case ActionType.EventChange:
                    await HandleEditComplete(args);
                    break;
                case ActionType.EventRemove:
                    await HandleDeleteComplete(args);
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnActionComplete: {ex.Message}");
        }
    }

    protected async Task HandleCreateComplete(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.AddedRecords?.Any() == true)
        {
            foreach (var addedEvent in args.AddedRecords)
            {
                await JSRuntime.InvokeVoidAsync("console.log", $"Creating new lesson, RecurrenceRule: {addedEvent.RecurrenceRule}");

                var createdEvent = await BffDashboard.CreateLessonAsync(addedEvent);
                if (createdEvent == null)
                {
                    await DialogService.ShowErrorAsync("Failed to create lesson", "Please try again.");
                    return;
                }
            }
            await LoadData();
        }
    }

    protected async Task HandleEditComplete(ActionEventArgs<ScheduleEvent> args)
    {
        // Handle changed records (editing existing events or updating recurring series)
        if (args.ChangedRecords?.Any() == true)
        {
            foreach (var changedEvent in args.ChangedRecords)
            {
                await JSRuntime.InvokeVoidAsync("console.log", $"Updating event ID: {changedEvent.Id}, RecurrenceRule: {changedEvent.RecurrenceRule}, RecurrenceException: {changedEvent.RecurrenceException}");

                var success = await BffDashboard.UpdateLessonAsync(changedEvent.Id, changedEvent);
                if (!success)
                {
                    await DialogService.ShowErrorAsync("Failed to update lesson", "Please try again.");
                    return;
                }
            }
        }

        // Handle added records (creating exception instances for recurring events)
        if (args.AddedRecords?.Any() == true)
        {
            foreach (var addedEvent in args.AddedRecords)
            {
                await JSRuntime.InvokeVoidAsync("console.log", $"Creating exception event, RecurrenceID: {addedEvent.RecurrenceID}");

                var createdEvent = await BffDashboard.CreateLessonAsync(addedEvent);
                if (createdEvent == null)
                {
                    await DialogService.ShowErrorAsync("Failed to create lesson exception", "Please try again.");
                    return;
                }
            }
        }

        await LoadData();
    }

    protected async Task OnPopupOpen(PopupOpenEventArgs<ScheduleEvent> args)
    {
        try
        {
            if (args.Type == PopupType.QuickInfo)
            {
                // Call JavaScript function to fix popup spacing and adaptive behavior
                await JSRuntime.InvokeVoidAsync("fixQuickInfoPopup", IsAdmin);
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnPopupOpen: {ex.Message}");
        }
    }

    protected async Task OnDragged(DragEventArgs<ScheduleEvent> args)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("console.log", $"🔄 Event dragged - ID: {args.Data.Id}, New Start: {args.Data.StartTime}, New End: {args.Data.EndTime}, RecurrenceRule: {args.Data.RecurrenceRule}, RecurrenceID: {args.Data.RecurrenceID}");

            // Check if user has permission to edit events
            if (!CanEditEvents)
            {
                await DialogService.ShowWarningAsync("You don't have permission to move lessons.", "Please contact an administrator for access.");
                args.Cancel = true;
                return;
            }

            // Handle recurring events
            if (!string.IsNullOrEmpty(args.Data.RecurrenceRule) || args.Data.RecurrenceID.HasValue)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "📅 Dragging recurring event - this will create an exception for this occurrence");

                // For recurring events, dragging creates an exception occurrence
                // The Syncfusion scheduler automatically handles this by creating a new event with RecurrenceID
                // We just need to save it to the database
            }

            // Update the lesson in the database
            var success = await BffDashboard.UpdateLessonAsync(args.Data.Id, args.Data);
            if (!success)
            {
                await DialogService.ShowErrorAsync("Failed to update lesson", "The lesson could not be moved. Please try again.");
                args.Cancel = true;
                return;
            }

            await JSRuntime.InvokeVoidAsync("console.log", $"✅ Lesson {args.Data.Id} successfully moved to {args.Data.StartTime}");

            // Refresh the data to ensure UI is in sync with database
            await LoadData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnDragged: {ex.Message}");
            await DialogService.ShowErrorAsync("Error moving lesson", ex.Message);
            args.Cancel = true;
        }
    }

    protected async Task HandleDeleteComplete(ActionEventArgs<ScheduleEvent> args)
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("console.log", $"HandleDeleteComplete - ActionType: {args.ActionType}");
            await JSRuntime.InvokeVoidAsync("console.log", $"DeletedRecords count: {args.DeletedRecords?.Count() ?? 0}");
            await JSRuntime.InvokeVoidAsync("console.log", $"ChangedRecords count: {args.ChangedRecords?.Count() ?? 0}");
            await JSRuntime.InvokeVoidAsync("console.log", $"AddedRecords count: {args.AddedRecords?.Count() ?? 0}");

            // Handle deleted records (complete series deletion or single non-recurring event deletion)
            if (args.DeletedRecords?.Any() == true)
            {
                foreach (var deletedEvent in args.DeletedRecords)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Deleting event ID: {deletedEvent.Id}, Subject: {deletedEvent.Subject}, RecurrenceRule: {deletedEvent.RecurrenceRule}");

                    var success = await BffDashboard.DeleteLessonAsync(deletedEvent.Id);
                    if (!success)
                    {
                        await DialogService.ShowErrorAsync("Failed to delete lesson", "Please try again.");
                        await LoadData(); // Refresh to restore UI state
                        return;
                    }
                }
            }

            // Handle changed records (when deleting a single occurrence from recurring series)
            // This updates the parent recurring event's RecurrenceException field to exclude the deleted occurrence
            if (args.ChangedRecords?.Any() == true)
            {
                foreach (var changedEvent in args.ChangedRecords)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Updating recurring event ID: {changedEvent.Id}, Subject: {changedEvent.Subject}");
                    await JSRuntime.InvokeVoidAsync("console.log", $"RecurrenceRule: {changedEvent.RecurrenceRule}");
                    await JSRuntime.InvokeVoidAsync("console.log", $"RecurrenceException: {changedEvent.RecurrenceException}");

                    var success = await BffDashboard.UpdateLessonAsync(changedEvent.Id, changedEvent);
                    if (!success)
                    {
                        await DialogService.ShowErrorAsync("Failed to update recurring lesson", "Please try again.");
                        await LoadData(); // Refresh to restore UI state
                        return;
                    }
                }
            }

            // Refresh data after successful operations
            await LoadData();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in HandleDeleteComplete: {ex.Message}");
            await DialogService.ShowErrorAsync("Error deleting lesson", ex.Message);
            await LoadData(); // Refresh to restore UI state
        }
    }

    protected string GetPageTitle(ClaimsPrincipal user)
    {
        if (user.IsInRole(UserRoleEnum.Tutor))
        {
            return "My Lessons - Tutor View";
        }
        else if (user.IsInRole(UserRoleEnum.Student))
        {
            return "My Lessons - Student View";
        }
        else if (user.IsInRole(UserRoleEnum.Administrator))
        {
            return "Lesson Time Table - Admin View";
        }
        else
        {
            return "Lesson Time Table";
        }
    }

    protected string GetShortPageTitle(ClaimsPrincipal user)
    {
        if (user.IsInRole(UserRoleEnum.Tutor))
        {
            return "My Lessons";
        }
        else if (user.IsInRole(UserRoleEnum.Student))
        {
            return "My Lessons";
        }
        else if (user.IsInRole(UserRoleEnum.Administrator))
        {
            return "Lessons";
        }
        else
        {
            return "Lessons";
        }
    }

    protected void ToggleScheduleView()
    {
        currentView = currentView == View.Agenda ? View.Week : View.Agenda;
    }

    // Handle responsive view switching based on screen size
    protected async Task OnBreakpointChanged()
    {
        try
        {
            // Automatically switch views based on screen size
            // Small = Mobile (Agenda view), Medium/Large = Tablet/Desktop (Week view)
            if (activeBreakpoint == "Small")
            {
                // Mobile: Switch to Agenda view
                if (currentView != View.Agenda)
                {
                    currentView = View.Agenda;
                    StateHasChanged();
                }
            }
            else if (activeBreakpoint == "Medium" || activeBreakpoint == "Large")
            {
                // Tablet/Desktop: Switch to Week view
                if (currentView != View.Week)
                {
                    currentView = View.Week;
                    StateHasChanged();
                }
            }

            await JSRuntime.InvokeVoidAsync("console.log", $"Breakpoint changed to: {activeBreakpoint}, View: {currentView}");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnBreakPointChanged: {ex.Message}");
        }
    }

    // Set initial view based on current breakpoint
    protected void SetInitialView()
    {
        try
        {
            // Set view based on current breakpoint
            if (activeBreakpoint == "Small")
            {
                // Mobile: Set to Agenda view
                currentView = View.Agenda;
            }
            else if (activeBreakpoint == "Medium" || activeBreakpoint == "Large")
            {
                // Tablet/Desktop: Set to Week view
                currentView = View.Week;
            }

            StateHasChanged();
            JSRuntime.InvokeVoidAsync("console.log", $"Initial view set - Breakpoint: {activeBreakpoint}, View: {currentView}");
        }
        catch (Exception ex)
        {
            JSRuntime.InvokeVoidAsync("console.error", $"Error in SetInitialView: {ex.Message}");
        }
    }

    protected string GetTutorColor(int? tutorId)
    {
        if (tutorId.HasValue)
        {
            var tutor = tutors.FirstOrDefault(t => t.TutorId == tutorId.Value);
            return tutor?.Color ?? "#6C757D";
        }
        return "#6C757D";
    }

    // QuickInfo footer button handlers
    protected async Task OnQuickEditClick(ScheduleEvent? eventData)
    {
        if (eventData != null && scheduleRef != null)
        {
            // Use Syncfusion's built-in edit functionality
            await scheduleRef.OpenEditorAsync(eventData, CurrentAction.Save);
        }
    }

    protected async Task OnQuickDeleteClick(ScheduleEvent? eventData)
    {
        if (eventData != null && scheduleRef != null)
        {
            // Show confirmation dialog before deleting
            var message = $"Are you sure you want to delete the lesson '{eventData.Subject}'?";
            var details = $"Student: {eventData.StudentName}\nTutor: {eventData.TutorName}\nTime: {eventData.StartTime:MMM dd, yyyy h:mm tt}";

            var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                message,
                details,
                "Delete Lesson");

            if (confirmed)
            {
                // Use Syncfusion's built-in delete functionality
                await scheduleRef.DeleteEventAsync(eventData);
            }
        }
    }

    public void Dispose()
    {
        if (_currentInstance == this)
        {
            _currentInstance = null;
        }
    }
}
