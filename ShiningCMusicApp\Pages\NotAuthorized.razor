@page "/not-authorized"
@using ShiningCMusicApp.Services
@inherits NotAuthorizedBase

<PageTitle>Access Denied</PageTitle>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i> Access Denied
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="bi bi-lock" style="font-size: 4rem; color: var(--bs-warning);"></i>
                    </div>
                    <h5 class="card-title">You don't have permission to access this page</h5>
                    <p class="card-text text-muted">
                        The page you're trying to access requires different permissions than your current role allows.
                    </p>
                    <div class="mt-4">
                        <button class="btn btn-primary me-2" style="min-width: 150px" @onclick="GoToHome">
                            <i class="bi bi-house"></i> Go to Home
                        </button>
                        <button class="btn btn-outline-secondary" style="min-width: 150px" @onclick="GoBack">
                            <i class="bi bi-arrow-left"></i> Continue
                        </button>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="text-muted">
                        <small>
                            <strong>Need different access?</strong><br>
                            Contact your administrator if you believe you should have access to this page.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

