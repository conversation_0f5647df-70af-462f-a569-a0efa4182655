using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp.Services;

namespace ShiningCMusicApp.Pages;

public partial class NotAuthorizedBase : ComponentBase
{
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected BffAuthenticationStateProvider AuthStateProvider { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;

    protected void GoToHome()
    {
        Navigation.NavigateTo("/");
    }

    protected void GoToLessons()
    {
        Navigation.NavigateTo("/lessons");
    }
    protected async Task GoBack()
    {
        // For authorization errors, going back might create a loop
        // Instead, try to navigate to a safe page based on user role
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true)
        {
            // Navigate to lessons page for authenticated users (most common safe page)
            Navigation.NavigateTo("/lessons");
        }
        else
        {
            // Navigate to home for unauthenticated users
            Navigation.NavigateTo("/");
        }
    }
}
