using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp.Services;

namespace ShiningCMusicApp.Pages;

public partial class NotFoundPageBase : ComponentBase
{
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;

    protected void GoToHome()
    {
        Navigation.NavigateTo("/");
    }

    protected async Task GoBack()
    {
        // Try to use browser history first, but with a fallback
        try
        {
            // Check if there's history to go back to
            var hasHistory = await JSRuntime.InvokeAsync<bool>("eval", "window.history.length > 1");

            if (hasHistory)
            {
                await JSRuntime.InvokeVoidAsync("history.back");
            }
            else
            {
                // No history available, go to home page
                Navigation.NavigateTo("/");
            }
        }
        catch
        {
            // If JavaScript fails for any reason, fallback to home
            Navigation.NavigateTo("/");
        }
    }
}
