using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Enums;

namespace ShiningCMusicApp.Pages
{
    public class SidebarThemeOption
    {
        public int Value { get; set; }
        public string Text { get; set; } = string.Empty;
        public string PreviewColor { get; set; } = string.Empty;
    }

    public partial class Settings : ComponentBase
    {
        [Inject] private IBffSettingsService BffSettingsService { get; set; } = default!;
        [Inject] private IDialogService DialogService { get; set; } = default!;
        [Inject] private AuthenticationStateProvider AuthenticationStateProvider { get; set; } = default!;
        [Inject] private ISidebarThemeService SidebarThemeService { get; set; } = default!;

        private List<ConfigGroup>? configGroups;
        private Dictionary<int, string> configValues = new();
        private Dictionary<string, bool> backgroundProcessorStates = new();
        private bool isLoading = true;
        private bool hasChanges = false;
        private string? errorMessage = null;
        private int selectedTabIndex = 0;

        private List<SidebarThemeOption> sidebarThemeOptions = new();

        protected override async Task OnInitializedAsync()
        {
            InitializeSidebarThemeOptions();
            await LoadConfigurationsAsync();
        }

        private void InitializeSidebarThemeOptions()
        {
            sidebarThemeOptions = Enum.GetValues<SidebarTheme>()
                .Select(theme => new SidebarThemeOption
                {
                    Value = (int)theme,
                    Text = theme.GetDescription(),
                    PreviewColor = theme.GetPreviewColor()
                })
                .ToList();
        }

        private string GetCustomColor1Value()
        {
            var config = configGroups?.SelectMany(g => g.Configs).FirstOrDefault(c => c.Key == "CustomSidebarColor1");
            if (config != null && configValues.TryGetValue(config.ConfigId, out var value))
            {
                return value;
            }
            return "#6c5ce7"; // Default
        }

        private string GetCustomColor2Value()
        {
            var config = configGroups?.SelectMany(g => g.Configs).FirstOrDefault(c => c.Key == "CustomSidebarColor2");
            if (config != null && configValues.TryGetValue(config.ConfigId, out var value))
            {
                return value;
            }
            return "#a29bfe"; // Default
        }

        private async void OnCustomColor1Change(string color)
        {
            var config = configGroups?.SelectMany(g => g.Configs).FirstOrDefault(c => c.Key == "CustomSidebarColor1");
            if (config != null)
            {
                configValues[config.ConfigId] = color;
                hasChanges = true;

                // Apply theme immediately if Custom theme is selected
                var themeConfig = configGroups?.SelectMany(g => g.Configs).FirstOrDefault(c => c.Key == "SidebarTheme");
                if (themeConfig != null && GetIntValue(themeConfig.ConfigId) == (int)SidebarTheme.Custom)
                {
                    await SidebarThemeService.ApplyThemeAsync(SidebarTheme.Custom);
                }
            }
            StateHasChanged();
        }

        private async void OnCustomColor2Change(string color)
        {
            var config = configGroups?.SelectMany(g => g.Configs).FirstOrDefault(c => c.Key == "CustomSidebarColor2");
            if (config != null)
            {
                configValues[config.ConfigId] = color;
                hasChanges = true;

                // Apply theme immediately if Custom theme is selected
                var themeConfig = configGroups?.SelectMany(g => g.Configs).FirstOrDefault(c => c.Key == "SidebarTheme");
                if (themeConfig != null && GetIntValue(themeConfig.ConfigId) == (int)SidebarTheme.Custom)
                {
                    await SidebarThemeService.ApplyThemeAsync(SidebarTheme.Custom);
                }
            }
            StateHasChanged();
        }



        private async Task LoadConfigurationsAsync()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                var settingsData = await BffSettingsService.GetSettingsDataAsync();
                configGroups = settingsData?.ConfigGroups ?? new List<ConfigGroup>();

                // Load current values
                configValues.Clear();
                backgroundProcessorStates.Clear();

                foreach (var group in configGroups)
                {
                    foreach (var config in group.Configs)
                    {
                        configValues[config.ConfigId] = config.Value ?? "";

                        // Track background processor states
                        if (group.GroupId == (int)ConfigGroupId.BackgroundProcessors && config.Key.EndsWith("Enabled"))
                        {
                            var processorName = config.Key.Replace("Enabled", "");
                            backgroundProcessorStates[processorName] = bool.TryParse(config.Value, out var enabled) && enabled;
                        }
                    }
                }

                hasChanges = false;
            }
            catch (UnauthorizedAccessException ex)
            {
                errorMessage = "Authentication failed. Please ensure you are logged in as an administrator.";
                Console.WriteLine($"Authentication error: {ex.Message}");
            }
            catch (Exception ex)
            {
                errorMessage = $"Failed to load configuration settings: {ex.Message}";
                Console.WriteLine($"Error loading configurations: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        private string[] GetBackgroundProcessors()
        {
            return new[] { "LessonCleanup", "TutorCleanup", "StudentCleanup", "PaymentReminder" };
        }

        private string GetProcessorDisplayName(string processor)
        {
            return processor switch
            {
                "LessonCleanup" => "Lesson Cleanup",
                "TutorCleanup" => "Tutor Cleanup",
                "StudentCleanup" => "Student Cleanup",
                "PaymentReminder" => "Payment Reminders",
                _ => processor
            };
        }

        private string GetProcessorDescription(string processor)
        {
            return processor switch
            {
                "LessonCleanup" => "Automatically removes old archived lessons",
                "TutorCleanup" => "Automatically removes old archived tutors",
                "StudentCleanup" => "Automatically removes old archived students",
                "PaymentReminder" => "Sends payment reminder emails to students",
                _ => ""
            };
        }

        private bool GetBoolValue(int configId)
        {
            if (configValues.TryGetValue(configId, out var value))
            {
                return bool.TryParse(value, out var boolValue) && boolValue;
            }
            return false;
        }

        private void SetBoolValue(int configId, bool value)
        {
            configValues[configId] = value.ToString();
            hasChanges = true;
            StateHasChanged();
        }

        private int GetIntValue(int configId)
        {
            if (configValues.TryGetValue(configId, out var value))
            {
                return int.TryParse(value, out var intValue) ? intValue : 0;
            }
            return 0;
        }

        private async void SetIntValue(int configId, int value)
        {
            configValues[configId] = value.ToString();
            hasChanges = true;

            // Check if this is a sidebar theme change and apply it immediately
            var config = configGroups?.SelectMany(g => g.Configs).FirstOrDefault(c => c.ConfigId == configId);
            if (config?.Key == "SidebarTheme" && Enum.IsDefined(typeof(SidebarTheme), value))
            {
                var theme = (SidebarTheme)value;
                await SidebarThemeService.ApplyThemeAsync(theme);
            }

            StateHasChanged();
        }

        private void OnBackgroundProcessorToggle(string processor, bool enabled)
        {
            backgroundProcessorStates[processor] = enabled;
            hasChanges = true;
            StateHasChanged();
        }

        private void OnConfigValueChange(int configId, string value)
        {
            configValues[configId] = value;
            hasChanges = true;
            StateHasChanged();
        }

        private async Task SaveChangesAsync()
        {
            try
            {
                isLoading = true;
                StateHasChanged();

                var updates = new List<ShiningCMusicCommon.Models.ConfigUpdateRequest>();

                // Update all configuration values (including background processor states)
                foreach (var group in configGroups!)
                {
                    foreach (var config in group.Configs)
                    {
                        string newValue = config.Value ?? "";

                        // Check if this is a background processor enabled setting
                        if (group.GroupId == (int)ConfigGroupId.BackgroundProcessors && config.Key.EndsWith("Enabled"))
                        {
                            var processorName = config.Key.Replace("Enabled", "");
                            if (backgroundProcessorStates.TryGetValue(processorName, out var enabled))
                            {
                                newValue = enabled.ToString();
                            }
                        }
                        else if (configValues.TryGetValue(config.ConfigId, out var userValue))
                        {
                            newValue = userValue;
                        }

                        // Add to updates if value changed
                        if (newValue != config.Value)
                        {
                            updates.Add(new ShiningCMusicCommon.Models.ConfigUpdateRequest
                            {
                                ConfigId = config.ConfigId,
                                Value = newValue
                            });
                        }
                    }
                }

                if (updates.Any())
                {
                    var configUpdates = updates.Select(u => new ConfigUpdate
                    {
                        ConfigId = u.ConfigId,
                        Value = u.Value
                    }).ToList();

                    var result = await BffSettingsService.BatchUpdateConfigsAsync(configUpdates);
                    if (!result.Success)
                    {
                        errorMessage = result.Message ?? "Failed to save some configuration changes";
                        return;
                    }
                }

                // Show success message without dialog for now
                Console.WriteLine("Configuration settings saved successfully");
                hasChanges = false;
                await LoadConfigurationsAsync();
            }
            catch (Exception ex)
            {
                errorMessage = $"Failed to save configuration settings: {ex.Message}";
                Console.WriteLine($"Save error: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }
    }
}
