using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffConfigService : IBffConfigService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffConfigService> _logger;
        private readonly string _baseUrl;

        public BffConfigService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffConfigService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<ConfigGroup>> GetConfigGroupsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching config groups from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/config/groups");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var groups = JsonSerializer.Deserialize<List<ConfigGroup>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return groups ?? new List<ConfigGroup>();
                }

                _logger.LogWarning("Failed to fetch config groups. Status: {StatusCode}", response.StatusCode);
                return new List<ConfigGroup>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching config groups from BFF");
                return new List<ConfigGroup>();
            }
        }

        public async Task<ConfigGroup?> GetConfigGroupAsync(int groupId)
        {
            try
            {
                _logger.LogInformation("Fetching config group {GroupId} from BFF", groupId);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/config/groups/{groupId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<ConfigGroup>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch config group {GroupId}. Status: {StatusCode}", groupId, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching config group {GroupId} from BFF", groupId);
                return null;
            }
        }

        public async Task<List<Config>> GetAllConfigsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching all configs from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/config");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var configs = JsonSerializer.Deserialize<List<Config>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return configs ?? new List<Config>();
                }

                _logger.LogWarning("Failed to fetch all configs. Status: {StatusCode}", response.StatusCode);
                return new List<Config>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching all configs from BFF");
                return new List<Config>();
            }
        }

        public async Task<Config?> GetConfigAsync(int groupId, string key)
        {
            try
            {
                _logger.LogInformation("Fetching config {Key} from group {GroupId} from BFF", key, groupId);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/config/{groupId}/{Uri.EscapeDataString(key)}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Config>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch config {Key} from group {GroupId}. Status: {StatusCode}", key, groupId, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching config {Key} from group {GroupId} from BFF", key, groupId);
                return null;
            }
        }

        public async Task<Config?> CreateConfigAsync(Config config)
        {
            try
            {
                _logger.LogInformation("Creating config via BFF");
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/config");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(config);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Config>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to create config. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating config via BFF");
                return null;
            }
        }

        public async Task<bool> UpdateConfigAsync(int configId, ConfigUpdateRequest request)
        {
            try
            {
                _logger.LogInformation("Updating config {ConfigId} via BFF", configId);
                var httpRequest = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/config/{configId}");
                httpRequest.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(request);
                httpRequest.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(httpRequest);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Config {ConfigId} updated successfully", configId);
                    return true;
                }

                _logger.LogWarning("Failed to update config {ConfigId}. Status: {StatusCode}", configId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating config {ConfigId} via BFF", configId);
                return false;
            }
        }

        public async Task<bool> UpdateConfigsAsync(List<ConfigUpdateRequest> requests)
        {
            try
            {
                _logger.LogInformation("Batch updating configs via BFF");
                var httpRequest = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/config/batch");
                httpRequest.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(requests);
                httpRequest.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(httpRequest);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Configs batch updated successfully");
                    return true;
                }

                _logger.LogWarning("Failed to batch update configs. Status: {StatusCode}", response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch updating configs via BFF");
                return false;
            }
        }

        public async Task<bool> DeleteConfigAsync(int configId)
        {
            try
            {
                _logger.LogInformation("Deleting config {ConfigId} via BFF", configId);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/config/{configId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Config {ConfigId} deleted successfully", configId);
                    return true;
                }

                _logger.LogWarning("Failed to delete config {ConfigId}. Status: {StatusCode}", configId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting config {ConfigId} via BFF", configId);
                return false;
            }
        }

        public async Task<bool> IsBackgroundProcessorEnabledAsync(string processorName)
        {
            try
            {
                _logger.LogInformation("Checking background processor {ProcessorName} status from BFF", processorName);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/config/background-processor/{Uri.EscapeDataString(processorName)}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<bool>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result;
                }

                _logger.LogWarning("Failed to check background processor {ProcessorName} status. Status: {StatusCode}", processorName, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking background processor {ProcessorName} status from BFF", processorName);
                return false;
            }
        }
    }
}
