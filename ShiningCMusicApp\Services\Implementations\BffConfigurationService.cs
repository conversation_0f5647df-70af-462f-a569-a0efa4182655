using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffConfigurationService : IBffConfigurationService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffConfigurationService> _logger;
        private readonly string _baseUrl;

        public BffConfigurationService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffConfigurationService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<AppConfiguration?> GetConfigurationAsync()
        {
            try
            {
                _logger.LogInformation("Fetching application configuration from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/configuration");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<AppConfiguration>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch application configuration. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching application configuration from BFF");
                return null;
            }
        }
    }
}
