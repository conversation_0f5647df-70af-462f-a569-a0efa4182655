using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text.Json;
using Microsoft.AspNetCore.Components.WebAssembly.Http;
using Microsoft.Extensions.Configuration;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffDashboardService : IBffDashboardService
    {
        private readonly IBffLessonsService _lessonsService;
        private readonly IBffStudentsService _studentsService;
        private readonly IBffTutorsService _tutorsService;
        private readonly IBffSubjectsService _subjectsService;
        private readonly IBffLocationsService _locationsService;
        private readonly ILogger<BffDashboardService> _logger;
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public BffDashboardService(
            IBffLessonsService lessonsService,
            IBffStudentsService studentsService,
            IBffTutorsService tutorsService,
            IBffSubjectsService subjectsService,
            IBffLocationsService locationsService,
            ILogger<BffDashboardService> logger,
            HttpClient httpClient,
            IConfiguration configuration)
        {
            _lessonsService = lessonsService;
            _studentsService = studentsService;
            _tutorsService = tutorsService;
            _subjectsService = subjectsService;
            _locationsService = locationsService;
            _logger = logger;
            _httpClient = httpClient;
            _baseUrl = configuration["BffBaseUrl"] ?? "https://localhost:5140/bff";
        }

        public async Task<DashboardData?> GetDashboardDataAsync()
        {
            try
            {
                _logger.LogInformation("Fetching dashboard data from BFF controller with role-based filtering");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/dashboard");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var dashboardData = JsonSerializer.Deserialize<DashboardData>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Dashboard data loaded successfully from BFF controller. Lessons: {LessonCount}, Students: {StudentCount}",
                        dashboardData?.Lessons?.Count ?? 0, dashboardData?.Students?.Count ?? 0);

                    return dashboardData;
                }
                else
                {
                    _logger.LogWarning("Failed to fetch dashboard data from BFF controller. Status: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching dashboard data from BFF controller");
                return null;
            }
        }

        public async Task<DashboardStats?> GetQuickStatsAsync()
        {
            try
            {
                _logger.LogInformation("Calculating quick stats from individual BFF services");

                // Get lessons to calculate stats
                var lessons = await _lessonsService.GetLessonsAsync();
                var students = await _studentsService.GetStudentsAsync();
                var tutors = await _tutorsService.GetTutorsAsync();
                var subjects = await _subjectsService.GetSubjectsAsync();

                var now = DateTime.Now;
                var today = now.Date;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                var weekEnd = weekStart.AddDays(7);

                var stats = new DashboardStats
                {
                    TotalLessons = lessons.Count,
                    TotalStudents = students.Count,
                    TotalTutors = tutors.Count,
                    TotalSubjects = subjects.Count,
                    UpcomingLessons = lessons.Count(l => l.StartTime > now),
                    TodayLessons = lessons.Count(l => l.StartTime.Date == today),
                    ThisWeekLessons = lessons.Count(l => l.StartTime.Date >= weekStart && l.StartTime.Date < weekEnd)
                };

                _logger.LogInformation("Quick stats calculated successfully. Total lessons: {TotalLessons}, Today: {TodayLessons}",
                    stats.TotalLessons, stats.TodayLessons);

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating quick stats from individual BFF services");
                return null;
            }
        }

        // Individual data access methods for backward compatibility
        public async Task<List<ScheduleEvent>> GetLessonsAsync()
        {
            return await _lessonsService.GetLessonsAsync();
        }

        public async Task<List<Student>> GetStudentsAsync()
        {
            return await _studentsService.GetStudentsAsync();
        }

        public async Task<List<Tutor>> GetTutorsAsync()
        {
            return await _tutorsService.GetTutorsAsync();
        }

        public async Task<List<Subject>> GetSubjectsAsync()
        {
            return await _subjectsService.GetSubjectsAsync();
        }

        public async Task<List<Location>> GetLocationsAsync()
        {
            return await _locationsService.GetLocationsAsync();
        }

        // Lesson Management
        public async Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson)
        {
            return await _lessonsService.CreateLessonAsync(lesson);
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            return await _lessonsService.UpdateLessonAsync(id, lesson);
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            return await _lessonsService.DeleteLessonAsync(id);
        }
    }
}
