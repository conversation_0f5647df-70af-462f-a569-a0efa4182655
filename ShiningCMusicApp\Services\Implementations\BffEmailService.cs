using Microsoft.AspNetCore.Components.WebAssembly.Http;
using Microsoft.Extensions.Logging;
using ShiningCMusicApp.Services.Interfaces;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffEmailService : IBffEmailService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffEmailService> _logger;
        private readonly string _baseUrl;

        public BffEmailService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffEmailService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<bool> SendScheduleReadyEmailAsync(int tutorId)
        {
            try
            {
                _logger.LogInformation("Sending schedule ready email to tutor {TutorId} via Web BFF", tutorId);

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/email/send-schedule-ready/{tutorId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Schedule ready email sent successfully to tutor {TutorId}", tutorId);
                    return true;
                }

                _logger.LogWarning("Failed to send schedule ready email to tutor {TutorId}. Status: {StatusCode}", tutorId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending schedule ready email to tutor {TutorId} via Web BFF", tutorId);
                return false;
            }
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true)
        {
            try
            {
                _logger.LogInformation("Sending email to {ToEmail} via Web BFF", toEmail);

                var requestData = new
                {
                    ToEmail = toEmail,
                    Subject = subject,
                    Body = body,
                    IsHtml = isHtml
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/email/send")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Email sent successfully to {ToEmail}", toEmail);
                    return true;
                }

                _logger.LogWarning("Failed to send email to {ToEmail}. Status: {StatusCode}", toEmail, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email to {ToEmail} via Web BFF", toEmail);
                return false;
            }
        }

        public async Task<bool> SendTemplateEmailAsync(int tutorId, string templateName)
        {
            try
            {
                _logger.LogInformation("Sending template email {TemplateName} to tutor {TutorId} via Web BFF", templateName, tutorId);

                var requestData = new
                {
                    TemplateName = templateName
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/email/send-template/{tutorId}")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Template email {TemplateName} sent successfully to tutor {TutorId}", templateName, tutorId);
                    return true;
                }

                _logger.LogWarning("Failed to send template email {TemplateName} to tutor {TutorId}. Status: {StatusCode}", templateName, tutorId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending template email {TemplateName} to tutor {TutorId} via Web BFF", templateName, tutorId);
                return false;
            }
        }
    }
}
