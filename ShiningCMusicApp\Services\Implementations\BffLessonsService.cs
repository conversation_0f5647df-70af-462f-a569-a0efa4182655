using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffLessonsService : IBffLessonsService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffLessonsService> _logger;
        private readonly string _baseUrl;

        public BffLessonsService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffLessonsService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<ScheduleEvent>> GetLessonsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching lessons from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/lessons");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var lessons = JsonSerializer.Deserialize<List<ScheduleEvent>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return lessons ?? new List<ScheduleEvent>();
                }

                _logger.LogWarning("Failed to fetch lessons. Status: {StatusCode}", response.StatusCode);
                return new List<ScheduleEvent>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching lessons from BFF");
                return new List<ScheduleEvent>();
            }
        }

        public async Task<ScheduleEvent?> GetLessonAsync(int id)
        {
            try
            {
                _logger.LogInformation("Fetching lesson {LessonId} from BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/lessons/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<ScheduleEvent>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch lesson {LessonId}. Status: {StatusCode}", id, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching lesson {LessonId} from BFF", id);
                return null;
            }
        }

        public async Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson)
        {
            try
            {
                _logger.LogInformation("Creating lesson via BFF");
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/lessons");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(lesson);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<ScheduleEvent>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to create lesson. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating lesson via BFF");
                return null;
            }
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            try
            {
                _logger.LogInformation("Updating lesson {LessonId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/lessons/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(lesson);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Lesson {LessonId} updated successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to update lesson {LessonId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating lesson {LessonId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting lesson {LessonId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/lessons/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Lesson {LessonId} deleted successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to delete lesson {LessonId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting lesson {LessonId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> ImportIcsAsync(Stream icsFileStream, string fileName)
        {
            try
            {
                _logger.LogInformation("Importing ICS file {FileName} via BFF", fileName);
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/lessons/import-ics");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var content = new MultipartFormDataContent();
                var streamContent = new StreamContent(icsFileStream);
                streamContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("text/calendar");
                content.Add(streamContent, "file", fileName);
                request.Content = content;

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("ICS file {FileName} imported successfully", fileName);
                    return true;
                }

                _logger.LogWarning("Failed to import ICS file {FileName}. Status: {StatusCode}", fileName, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing ICS file {FileName} via BFF", fileName);
                return false;
            }
        }

        public async Task<Stream?> ExportIcsAsync()
        {
            try
            {
                _logger.LogInformation("Exporting ICS file via BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/lessons/export-ics");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("ICS file exported successfully");
                    return await response.Content.ReadAsStreamAsync();
                }

                _logger.LogWarning("Failed to export ICS file. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting ICS file via BFF");
                return null;
            }
        }
    }
}
