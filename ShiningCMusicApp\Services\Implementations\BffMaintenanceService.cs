using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffMaintenanceService : IBffMaintenanceService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffMaintenanceService> _logger;
        private readonly string _baseUrl;

        public BffMaintenanceService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffMaintenanceService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<MaintenanceResult> CleanupAllLessonsAsync(int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Cleaning up lessons older than {Days} days via BFF", olderThanDays);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/maintenance/lessons?olderThanDays={olderThanDays}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<MaintenanceResult>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result ?? new MaintenanceResult { Message = "Unknown result", DeletedCount = 0, OlderThanDays = olderThanDays };
                }

                _logger.LogWarning("Failed to cleanup lessons. Status: {StatusCode}", response.StatusCode);
                return new MaintenanceResult { Message = $"Failed to cleanup lessons. Status: {response.StatusCode}", DeletedCount = 0, OlderThanDays = olderThanDays };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up lessons via BFF");
                return new MaintenanceResult { Message = $"Error: {ex.Message}", DeletedCount = 0, OlderThanDays = olderThanDays };
            }
        }

        public async Task<MaintenanceCountResult> GetAllLessonsCountAsync(int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Getting count of lessons older than {Days} days via BFF", olderThanDays);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/maintenance/lessons/count?olderThanDays={olderThanDays}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<MaintenanceCountResult>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result ?? new MaintenanceCountResult { Message = "Unknown result", Count = 0, OlderThanDays = olderThanDays };
                }

                _logger.LogWarning("Failed to get lessons count. Status: {StatusCode}", response.StatusCode);
                return new MaintenanceCountResult { Message = $"Failed to get lessons count. Status: {response.StatusCode}", Count = 0, OlderThanDays = olderThanDays };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lessons count via BFF");
                return new MaintenanceCountResult { Message = $"Error: {ex.Message}", Count = 0, OlderThanDays = olderThanDays };
            }
        }

        public async Task<MaintenanceResult> CleanupArchivedTutorsAsync(int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Cleaning up archived tutors older than {Days} days via BFF", olderThanDays);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/maintenance/tutors?olderThanDays={olderThanDays}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<MaintenanceResult>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result ?? new MaintenanceResult { Message = "Unknown result", DeletedCount = 0, OlderThanDays = olderThanDays };
                }

                _logger.LogWarning("Failed to cleanup archived tutors. Status: {StatusCode}", response.StatusCode);
                return new MaintenanceResult { Message = $"Failed to cleanup archived tutors. Status: {response.StatusCode}", DeletedCount = 0, OlderThanDays = olderThanDays };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up archived tutors via BFF");
                return new MaintenanceResult { Message = $"Error: {ex.Message}", DeletedCount = 0, OlderThanDays = olderThanDays };
            }
        }

        public async Task<MaintenanceCountResult> GetArchivedTutorsCountAsync(int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Getting count of archived tutors older than {Days} days via BFF", olderThanDays);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/maintenance/tutors/count?olderThanDays={olderThanDays}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<MaintenanceCountResult>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result ?? new MaintenanceCountResult { Message = "Unknown result", Count = 0, OlderThanDays = olderThanDays };
                }

                _logger.LogWarning("Failed to get archived tutors count. Status: {StatusCode}", response.StatusCode);
                return new MaintenanceCountResult { Message = $"Failed to get archived tutors count. Status: {response.StatusCode}", Count = 0, OlderThanDays = olderThanDays };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting archived tutors count via BFF");
                return new MaintenanceCountResult { Message = $"Error: {ex.Message}", Count = 0, OlderThanDays = olderThanDays };
            }
        }

        public async Task<MaintenanceResult> CleanupArchivedStudentsAsync(int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Cleaning up archived students older than {Days} days via BFF", olderThanDays);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/maintenance/students?olderThanDays={olderThanDays}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<MaintenanceResult>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result ?? new MaintenanceResult { Message = "Unknown result", DeletedCount = 0, OlderThanDays = olderThanDays };
                }

                _logger.LogWarning("Failed to cleanup archived students. Status: {StatusCode}", response.StatusCode);
                return new MaintenanceResult { Message = $"Failed to cleanup archived students. Status: {response.StatusCode}", DeletedCount = 0, OlderThanDays = olderThanDays };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up archived students via BFF");
                return new MaintenanceResult { Message = $"Error: {ex.Message}", DeletedCount = 0, OlderThanDays = olderThanDays };
            }
        }

        public async Task<MaintenanceCountResult> GetArchivedStudentsCountAsync(int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Getting count of archived students older than {Days} days via BFF", olderThanDays);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/maintenance/students/count?olderThanDays={olderThanDays}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<MaintenanceCountResult>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return result ?? new MaintenanceCountResult { Message = "Unknown result", Count = 0, OlderThanDays = olderThanDays };
                }

                _logger.LogWarning("Failed to get archived students count. Status: {StatusCode}", response.StatusCode);
                return new MaintenanceCountResult { Message = $"Failed to get archived students count. Status: {response.StatusCode}", Count = 0, OlderThanDays = olderThanDays };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting archived students count via BFF");
                return new MaintenanceCountResult { Message = $"Error: {ex.Message}", Count = 0, OlderThanDays = olderThanDays };
            }
        }
    }
}
