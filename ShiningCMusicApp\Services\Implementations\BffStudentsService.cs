using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffStudentsService : IBffStudentsService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffStudentsService> _logger;
        private readonly string _baseUrl;

        public BffStudentsService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffStudentsService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<Student>> GetStudentsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching students from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/students");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var students = JsonSerializer.Deserialize<List<Student>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return students ?? new List<Student>();
                }

                _logger.LogWarning("Failed to fetch students. Status: {StatusCode}", response.StatusCode);
                return new List<Student>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching students from BFF");
                return new List<Student>();
            }
        }

        public async Task<Student?> GetStudentAsync(int id)
        {
            try
            {
                _logger.LogInformation("Fetching student {StudentId} from BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/students/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Student>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch student {StudentId}. Status: {StatusCode}", id, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching student {StudentId} from BFF", id);
                return null;
            }
        }

        public async Task<Student?> CreateStudentAsync(Student student)
        {
            try
            {
                _logger.LogInformation("Creating student via BFF");
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/students");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(student);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Student>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to create student. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating student via BFF");
                return null;
            }
        }

        public async Task<bool> UpdateStudentAsync(int id, Student student)
        {
            try
            {
                _logger.LogInformation("Updating student {StudentId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/students/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(student);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Student {StudentId} updated successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to update student {StudentId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating student {StudentId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> DeleteStudentAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting student {StudentId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/students/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Student {StudentId} deleted successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to delete student {StudentId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting student {StudentId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> ArchiveStudentAsync(int id)
        {
            try
            {
                _logger.LogInformation("Archiving student {StudentId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/students/{id}/archive");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Student {StudentId} archived successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to archive student {StudentId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error archiving student {StudentId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> RestoreStudentAsync(int id)
        {
            try
            {
                _logger.LogInformation("Restoring student {StudentId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/students/{id}/restore");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Student {StudentId} restored successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to restore student {StudentId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring student {StudentId} via BFF", id);
                return false;
            }
        }

        public async Task<List<Student>> GetStudentsBySubjectAsync(int subjectId)
        {
            try
            {
                _logger.LogInformation("Fetching students by subject {SubjectId} from BFF", subjectId);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/students/by-subject/{subjectId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var students = JsonSerializer.Deserialize<List<Student>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return students ?? new List<Student>();
                }

                _logger.LogWarning("Failed to fetch students by subject {SubjectId}. Status: {StatusCode}", subjectId, response.StatusCode);
                return new List<Student>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching students by subject {SubjectId} from BFF", subjectId);
                return new List<Student>();
            }
        }
    }
}
