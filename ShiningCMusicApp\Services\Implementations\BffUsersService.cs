using Microsoft.AspNetCore.Components.WebAssembly.Http;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class BffUsersService : IBffUsersService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<BffUsersService> _logger;
        private readonly string _baseUrl;

        public BffUsersService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffUsersService> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<User>> GetUsersAsync()
        {
            try
            {
                _logger.LogInformation("Fetching users from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/users");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var users = JsonSerializer.Deserialize<List<User>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return users ?? new List<User>();
                }

                _logger.LogWarning("Failed to fetch users. Status: {StatusCode}", response.StatusCode);
                return new List<User>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching users from BFF");
                return new List<User>();
            }
        }

        public async Task<User?> GetUserAsync(string loginName)
        {
            try
            {
                _logger.LogInformation("Fetching user {LoginName} from BFF", loginName);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/users/{Uri.EscapeDataString(loginName)}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<User>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch user {LoginName}. Status: {StatusCode}", loginName, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user {LoginName} from BFF", loginName);
                return null;
            }
        }

        public async Task<User?> CreateUserAsync(User user)
        {
            try
            {
                _logger.LogInformation("Creating user via BFF");
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/users");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(user);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<User>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to create user. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user via BFF");
                return null;
            }
        }

        public async Task<bool> UpdateUserAsync(string loginName, User user)
        {
            try
            {
                _logger.LogInformation("Updating user {LoginName} via BFF", loginName);
                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/users/{Uri.EscapeDataString(loginName)}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(user);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("User {LoginName} updated successfully", loginName);
                    return true;
                }

                _logger.LogWarning("Failed to update user {LoginName}. Status: {StatusCode}", loginName, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {LoginName} via BFF", loginName);
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(string loginName)
        {
            try
            {
                _logger.LogInformation("Deleting user {LoginName} via BFF", loginName);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/users/{Uri.EscapeDataString(loginName)}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("User {LoginName} deleted successfully", loginName);
                    return true;
                }

                _logger.LogWarning("Failed to delete user {LoginName}. Status: {StatusCode}", loginName, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {LoginName} via BFF", loginName);
                return false;
            }
        }

        public async Task<List<UserRole>> GetUserRolesAsync()
        {
            try
            {
                _logger.LogInformation("Fetching user roles from BFF");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/users/roles");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var userRoles = JsonSerializer.Deserialize<List<UserRole>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return userRoles ?? new List<UserRole>();
                }

                _logger.LogWarning("Failed to fetch user roles. Status: {StatusCode}", response.StatusCode);
                return new List<UserRole>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user roles from BFF");
                return new List<UserRole>();
            }
        }

        public async Task<UserRole?> GetUserRoleAsync(int id)
        {
            try
            {
                _logger.LogInformation("Fetching user role {RoleId} from BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/users/roles/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<UserRole>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to fetch user role {RoleId}. Status: {StatusCode}", id, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user role {RoleId} from BFF", id);
                return null;
            }
        }

        public async Task<UserRole?> CreateUserRoleAsync(UserRole userRole)
        {
            try
            {
                _logger.LogInformation("Creating user role via BFF");
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/users/roles");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(userRole);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<UserRole>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                _logger.LogWarning("Failed to create user role. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user role via BFF");
                return null;
            }
        }

        public async Task<bool> UpdateUserRoleAsync(int id, UserRole userRole)
        {
            try
            {
                _logger.LogInformation("Updating user role {RoleId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/users/roles/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(userRole);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("User role {RoleId} updated successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to update user role {RoleId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user role {RoleId} via BFF", id);
                return false;
            }
        }

        public async Task<bool> DeleteUserRoleAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting user role {RoleId} via BFF", id);
                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/users/roles/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("User role {RoleId} deleted successfully", id);
                    return true;
                }

                _logger.LogWarning("Failed to delete user role {RoleId}. Status: {StatusCode}", id, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user role {RoleId} via BFF", id);
                return false;
            }
        }
    }
}
