namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffClientsService
    {
        Task<List<ClientInfo>> GetClientsAsync();
        Task<ClientInfo?> GetClientAsync(string clientId);
        Task<ClientCreateResult> CreateClientAsync(CreateClientRequest request);
        Task<bool> DeleteClientAsync(string clientId);
    }

    public class CreateClientRequest
    {
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public string? DisplayName { get; set; }
        public string? Description { get; set; }
        public int TokenLifetimeSeconds { get; set; } = 3600; // Default 1 hour
    }

    public class ClientInfo
    {
        public string ClientId { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public int TokenLifetimeSeconds { get; set; }
    }

    public class ClientCreateResult
    {
        public string Message { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public int TokenLifetimeSeconds { get; set; }
    }
}
