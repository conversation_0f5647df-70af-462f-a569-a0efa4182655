using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffDashboardService
    {
        Task<DashboardData?> GetDashboardDataAsync();
        Task<DashboardStats?> GetQuickStatsAsync();

        // Individual data access methods for backward compatibility
        Task<List<ScheduleEvent>> GetLessonsAsync();
        Task<List<Student>> GetStudentsAsync();
        Task<List<Tutor>> GetTutorsAsync();
        Task<List<Subject>> GetSubjectsAsync();
        Task<List<Location>> GetLocationsAsync();

        // Lesson Management
        Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);
    }


}
