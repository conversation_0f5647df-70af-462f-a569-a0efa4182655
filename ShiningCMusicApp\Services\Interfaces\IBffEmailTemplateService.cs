using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffEmailTemplateService
    {
        Task<List<EmailTemplate>> GetTemplatesAsync();
        Task<EmailTemplate?> GetTemplateAsync(string templateName);
        Task<bool> CreateTemplateAsync(EmailTemplate template);
        Task<bool> UpdateTemplateAsync(EmailTemplate template);
        Task<bool> DeleteTemplateAsync(string templateName);
        Task<string> PreviewTemplateAsync(string templateName, Dictionary<string, string> sampleData);

        // Attachment methods
        Task<List<EmailAttachment>> GetTemplateAttachmentsAsync(string templateName);
        Task<EmailAttachment?> AddAttachmentAsync(EmailAttachment attachment);
        Task<bool> DeleteAttachmentAsync(int attachmentId);
    }
}
