using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffLessonsService
    {
        // CRUD Operations
        Task<List<ScheduleEvent>> GetLessonsAsync();
        Task<ScheduleEvent?> GetLessonAsync(int id);
        Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);

        // ICS Import/Export
        Task<bool> ImportIcsAsync(Stream icsFileStream, string fileName);
        Task<Stream?> ExportIcsAsync();
    }
}
