using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IBffTutorsService
    {
        // CRUD Operations
        Task<List<Tutor>> GetTutorsAsync();
        Task<Tutor?> GetTutorAsync(int id);
        Task<Tutor?> CreateTutor<PERSON><PERSON>(<PERSON><PERSON> tutor);
        Task<bool> UpdateTutorAsync(int id, Tu<PERSON> tutor);
        Task<bool> DeleteTutorAsync(int id);

        // Archive/Restore Operations
        Task<bool> ArchiveTutorAsync(int id);
        Task<bool> RestoreTutorAsync(int id);

        // Subject-based filtering
        Task<List<Tutor>> GetTutorsBySubjectAsync(int subjectId);

        // Color management
        Task<bool> UpdateTutorColorAsync(int tutorId, string color);
    }
}
