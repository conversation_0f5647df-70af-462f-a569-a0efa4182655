<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.17" PrivateAssets="all" />
    <PackageReference Include="Syncfusion.Blazor.Grid" Version="29.2.11" />
	<PackageReference Include="Syncfusion.Blazor.Schedule" Version="29.2.11" />
	<PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="29.2.11" />
	<PackageReference Include="Syncfusion.Blazor.Themes" Version="29.2.11" />
	<PackageReference Include="Syncfusion.XlsIO.Net.Core" Version="29.2.11" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ShiningCMusicCommon\ShiningCMusicCommon.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\css\app.css">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Pages\Lessons.razor.cs">
      <DependentUpon>Lessons.razor</DependentUpon>
    </Compile>
    <Compile Update="Pages\Lessons.razor.Methods.cs">
      <DependentUpon>Lessons.razor</DependentUpon>
    </Compile>
    <Compile Update="Pages\Lessons.razor.ICS.cs">
      <DependentUpon>Lessons.razor</DependentUpon>
    </Compile>
    <Content Update="Pages\Lessons.razor.css">
      <DependentUpon>Lessons.razor</DependentUpon>
    </Content>
    <Compile Update="Pages\Home.razor.cs">
      <DependentUpon>Home.razor</DependentUpon>
    </Compile>
    <Compile Update="Pages\Admin.razor.cs">
      <DependentUpon>Admin.razor</DependentUpon>
    </Compile>
    <Compile Update="Pages\Admin.razor.Users.cs">
      <DependentUpon>Admin.razor</DependentUpon>
    </Compile>
    <Content Update="Pages\Admin.razor.css">
      <DependentUpon>Admin.razor</DependentUpon>
    </Content>
    <Compile Update="Pages\Students.razor.cs">
      <DependentUpon>Students.razor</DependentUpon>
    </Compile>
    <Compile Update="Pages\Tutors.razor.cs">
      <DependentUpon>Tutors.razor</DependentUpon>
    </Compile>
    <Compile Update="Pages\Login.razor.cs">
      <DependentUpon>Login.razor</DependentUpon>
    </Compile>
    <Content Update="Pages\Login.razor.css">
      <DependentUpon>Login.razor</DependentUpon>
    </Content>
    <Compile Update="Pages\NotAuthorized.razor.cs">
      <DependentUpon>NotAuthorized.razor</DependentUpon>
    </Compile>
    <Compile Update="Pages\EmailTemplates.razor.cs">
      <DependentUpon>EmailTemplates.razor</DependentUpon>
    </Compile>
    <Content Update="Pages\EmailTemplates.razor.css">
      <DependentUpon>EmailTemplates.razor</DependentUpon>
    </Content>
  </ItemGroup>

</Project>
