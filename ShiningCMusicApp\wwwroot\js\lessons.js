// Lessons page JavaScript functionality
// Custom QuickInfo popup and lesson management functions

window.showCustomQuickInfo = function(htmlContent, title) {
    // Create a modal-like overlay that matches Syncfusion's quick info styling
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        z-index: 1060;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        box-sizing: border-box;
    `;

    const popup = document.createElement('div');
    popup.style.cssText = `
        background: white;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        max-width: 350px;
        width: 100%;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
        padding: 20px;
        border: 1px solid #e0e0e0;
        animation: quickInfoSlideIn 0.2s ease-out;
    `;

    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.style.cssText = `
        position: absolute;
        top: 8px;
        right: 12px;
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #666;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s;
    `;

    closeButton.onmouseover = () => closeButton.style.backgroundColor = '#f0f0f0';
    closeButton.onmouseout = () => closeButton.style.backgroundColor = 'transparent';

    const content = document.createElement('div');
    content.innerHTML = htmlContent;

    popup.appendChild(closeButton);
    popup.appendChild(content);
    overlay.appendChild(popup);

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes quickInfoSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    `;
    document.head.appendChild(style);

    // Close handlers
    const closeModal = () => {
        popup.style.animation = 'quickInfoSlideOut 0.15s ease-in';
        setTimeout(() => {
            if (document.body.contains(overlay)) {
                document.body.removeChild(overlay);
            }
            if (document.head.contains(style)) {
                document.head.removeChild(style);
            }
        }, 150);
    };

    closeButton.onclick = closeModal;
    overlay.onclick = (e) => {
        if (e.target === overlay) closeModal();
    };

    // Add escape key handler
    const escapeHandler = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);

    document.body.appendChild(overlay);
};


// Global functions for edit and delete buttons
window.editLesson = function(lessonId) {
    // Close the quick info popup first
    const overlay = document.querySelector('div[style*="position: fixed"][style*="z-index: 1060"]');
    if (overlay) {
        overlay.remove();
    }

    // Call Blazor method to edit lesson
    DotNet.invokeMethodAsync('ShiningCMusicApp', 'EditLessonFromQuickInfo', lessonId);
};

window.deleteLesson = function(lessonId) {
    // Close the quick info popup first
    const overlay = document.querySelector('div[style*="position: fixed"][style*="z-index: 1060"]');
    if (overlay) {
        overlay.remove();
    }

    // Call Blazor method to delete lesson
    DotNet.invokeMethodAsync('ShiningCMusicApp', 'DeleteLessonFromQuickInfo', lessonId);
};

// Remove mobile adaptive classes to prevent full-screen QuickInfo popup
window.removeMobileAdaptiveClasses = function() {
    setTimeout(() => {
        const scheduleElement = document.querySelector('.e-schedule');
        if (scheduleElement) {
            // Remove classes that cause mobile full-screen behavior
            scheduleElement.classList.remove('e-device', 'e-adaptive');
        }
    }, 50);
};

// Initialize mobile adaptive class removal on page load
window.initMobileQuickInfoFix = function() {
    // Remove classes immediately
    window.removeMobileAdaptiveClasses();

    // Set up a mutation observer to watch for class changes
    const scheduleElement = document.querySelector('.e-schedule');
    if (scheduleElement && window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('e-device') || target.classList.contains('e-adaptive')) {
                        target.classList.remove('e-device', 'e-adaptive');
                    }
                }
            });
        });

        observer.observe(scheduleElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    }

    // Also set up periodic cleanup as backup
    setInterval(() => {
        window.removeMobileAdaptiveClasses();
    }, 1000);
};

// Fix QuickInfo popup spacing and adaptive behavior
window.fixQuickInfoPopup = function(isAdmin) {
    setTimeout(() => {
        // First remove mobile adaptive classes from schedule element
        const scheduleElement = document.querySelector('.e-schedule');
        if (scheduleElement) {
            scheduleElement.classList.remove('e-device', 'e-adaptive');
        }

        const popup = document.querySelector('.e-quick-popup-wrapper');
        if (popup) {
            // Remove adaptive/device classes that cause full-screen behavior
            popup.classList.remove('e-device', 'e-adaptive');

            // Set auto height and max-height to prevent extra space
            const popupElement = popup.querySelector('.e-event-popup');
            if (popupElement) {
                popupElement.style.height = 'auto';
                popupElement.style.maxHeight = window.innerWidth < 992 ? '70vh' : '80vh';
                popupElement.style.minHeight = 'auto';
            }

            // Ensure content area fits content tightly
            const contentElement = popup.querySelector('.e-popup-content');
            if (contentElement) {
                contentElement.style.height = 'auto';
                contentElement.style.minHeight = 'auto';
                contentElement.style.paddingBottom = '16px';
            }

            // Hide footer for non-admin users on desktop (mobile footer is hidden via CSS)
            let footerElement = popup.querySelector('.e-popup-footer');
            if (footerElement && !isAdmin) {
                // Check if we're on mobile (width < 992px to match CSS media query)
                const isMobile = window.innerWidth < 992;
                if (!isMobile) {
                    footerElement.style.setProperty('display', 'none', 'important');
                }
            }

            // On mobile, ensure popup is properly positioned and sized
            if (window.innerWidth < 992) {
                popup.style.setProperty('position', 'fixed', 'important');
                popup.style.setProperty('top', '50%', 'important');
                popup.style.setProperty('left', '50%', 'important');
                popup.style.setProperty('transform', 'translate(-50%, -50%)', 'important');
                popup.style.setProperty('width', '90%', 'important');
                popup.style.setProperty('max-width', '350px', 'important');
                popup.style.setProperty('height', 'auto', 'important');
                popup.style.setProperty('max-height', '70vh', 'important');

                // Aggressively hide header buttons on mobile
                const headerButtons = popup.querySelectorAll('.e-popup-header .e-btn, .e-popup-header button, .e-popup-header .e-close, .e-popup-header .e-icons');
                headerButtons.forEach(btn => {
                    btn.style.setProperty('display', 'none', 'important');
                    btn.style.setProperty('visibility', 'hidden', 'important');
                    btn.style.setProperty('opacity', '0', 'important');
                });

                // Remove extra footer spacing
                const footer = popup.querySelector('.e-popup-footer');
                if (footer && footer.children.length === 0) {
                    footer.style.setProperty('display', 'none', 'important');
                    footer.style.setProperty('padding', '0', 'important');
                    footer.style.setProperty('height', '0', 'important');
                }
            }
        }
    }, 100);
};

// Function to scroll to recurrence section with visual feedback
window.scrollToRecurrenceSection = function() {
    const recurrenceSection = document.getElementById('recurrence-section');
    if (recurrenceSection) {
        // Scroll to the section
        recurrenceSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
        });

        // Add a subtle highlight effect
        recurrenceSection.style.transition = 'background-color 0.3s ease';
        recurrenceSection.style.backgroundColor = '#e3f2fd';

        setTimeout(() => {
            recurrenceSection.style.backgroundColor = '';
        }, 1500);
    }
};
