using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/clients")]
    [Authorize]
    public class ClientsController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<ClientsController> _logger;

        public ClientsController(ApiClientService apiClient, ILogger<ClientsController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // POST: bff/clients
        [HttpPost]
        public async Task<IActionResult> CreateClient([FromBody] CreateClientRequest request)
        {
            try
            {
                _logger.LogInformation("Creating client {ClientId} via API", request.ClientId);
                var response = await _apiClient.PostAsync("clients", request);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<object>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return Ok(result);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create client {ClientId}. Status: {StatusCode}, Error: {Error}", request.ClientId, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.Conflict)
                {
                    return Conflict(new { message = "Client already exists" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to create client", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating client {ClientId} via API", request.ClientId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/clients
        [HttpGet]
        public async Task<IActionResult> GetClients()
        {
            try
            {
                _logger.LogInformation("Fetching clients from API");
                var clients = await _apiClient.GetJsonAsync<List<object>>("clients");
                return Ok(clients ?? new List<object>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving clients from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/clients/{clientId}
        [HttpGet("{clientId}")]
        public async Task<IActionResult> GetClient(string clientId)
        {
            try
            {
                _logger.LogInformation("Fetching client {ClientId} from API", clientId);
                var client = await _apiClient.GetJsonAsync<object>($"clients/{clientId}");
                
                if (client == null)
                {
                    return NotFound(new { message = "Client not found" });
                }

                return Ok(client);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving client {ClientId} from API", clientId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/clients/{clientId}
        [HttpDelete("{clientId}")]
        public async Task<IActionResult> DeleteClient(string clientId)
        {
            try
            {
                _logger.LogInformation("Deleting client {ClientId} via API", clientId);
                var response = await _apiClient.DeleteAsync($"clients/{clientId}");
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Client deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete client {ClientId}. Status: {StatusCode}, Error: {Error}", clientId, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Client not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete client", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting client {ClientId} via API", clientId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }

    public class CreateClientRequest
    {
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
        public string? DisplayName { get; set; }
        public string? Description { get; set; }
        public int TokenLifetimeSeconds { get; set; } = 3600; // Default 1 hour
    }
}
