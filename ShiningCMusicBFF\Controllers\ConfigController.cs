using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/config")]
    [Authorize]
    public class ConfigController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<ConfigController> _logger;

        public ConfigController(ApiClientService apiClient, ILogger<ConfigController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // GET: bff/config/groups
        [HttpGet("groups")]
        public async Task<ActionResult<IEnumerable<ConfigGroup>>> GetConfigGroups()
        {
            try
            {
                _logger.LogInformation("Fetching config groups from API");
                var groups = await _apiClient.GetJsonAsync<List<ConfigGroup>>("config/groups");
                return Ok(groups ?? new List<ConfigGroup>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving config groups from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/config/groups/{groupId}
        [HttpGet("groups/{groupId}")]
        public async Task<ActionResult<ConfigGroup>> GetConfigGroup(int groupId)
        {
            try
            {
                _logger.LogInformation("Fetching config group {GroupId} from API", groupId);
                var group = await _apiClient.GetJsonAsync<ConfigGroup>($"config/groups/{groupId}");
                
                if (group == null)
                {
                    return NotFound(new { message = "Config group not found" });
                }

                return Ok(group);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving config group {GroupId} from API", groupId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/config
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Config>>> GetAllConfigs()
        {
            try
            {
                _logger.LogInformation("Fetching all configs from API");
                var configs = await _apiClient.GetJsonAsync<List<Config>>("config");
                return Ok(configs ?? new List<Config>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all configs from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/config/{groupId}/{key}
        [HttpGet("{groupId}/{key}")]
        public async Task<ActionResult<Config>> GetConfig(int groupId, string key)
        {
            try
            {
                _logger.LogInformation("Fetching config {GroupId}:{Key} from API", groupId, key);
                var config = await _apiClient.GetJsonAsync<Config>($"config/{groupId}/{key}");
                
                if (config == null)
                {
                    return NotFound(new { message = "Config not found" });
                }

                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving config {GroupId}:{Key} from API", groupId, key);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/config/{configId}
        [HttpPut("{configId}")]
        public async Task<IActionResult> UpdateConfig(int configId, [FromBody] ConfigUpdateRequest request)
        {
            try
            {
                _logger.LogInformation("Updating config {ConfigId} via API", configId);
                var response = await _apiClient.PutAsync($"config/{configId}", request);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Config updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update config {ConfigId}. Status: {StatusCode}, Error: {Error}", configId, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Config not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to update config", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating config {ConfigId} via API", configId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/config/batch
        [HttpPut("batch")]
        public async Task<IActionResult> UpdateConfigs([FromBody] List<ConfigUpdateRequest> requests)
        {
            try
            {
                _logger.LogInformation("Batch updating {Count} configs via API", requests.Count);
                var response = await _apiClient.PutAsync("config/batch", requests);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return Ok(new { message = "Batch update completed", details = content });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to batch update configs. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to batch update configs", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch updating configs via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/config
        [HttpPost]
        public async Task<ActionResult<Config>> CreateConfig([FromBody] Config config)
        {
            try
            {
                _logger.LogInformation("Creating config via API");
                var response = await _apiClient.PostAsync("config", config);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdConfig = System.Text.Json.JsonSerializer.Deserialize<Config>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return CreatedAtAction(nameof(GetConfig), new { groupId = createdConfig?.GroupId, key = createdConfig?.Key }, createdConfig);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create config. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to create config", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating config via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/config/{configId}
        [HttpDelete("{configId}")]
        public async Task<IActionResult> DeleteConfig(int configId)
        {
            try
            {
                _logger.LogInformation("Deleting config {ConfigId} via API", configId);
                var response = await _apiClient.DeleteAsync($"config/{configId}");
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Config deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete config {ConfigId}. Status: {StatusCode}, Error: {Error}", configId, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Config not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete config", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting config {ConfigId} via API", configId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/config/background-processors/{processorName}/enabled
        [HttpGet("background-processors/{processorName}/enabled")]
        public async Task<ActionResult<bool>> IsBackgroundProcessorEnabled(string processorName)
        {
            try
            {
                _logger.LogInformation("Checking background processor {ProcessorName} status via API", processorName);
                var result = await _apiClient.GetJsonAsync<object>($"config/background-processors/{processorName}/enabled");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking background processor {ProcessorName} status via API", processorName);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
