using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/configuration")]
    public class ConfigurationController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<ConfigurationController> _logger;

        public ConfigurationController(ApiClientService apiClient, ILogger<ConfigurationController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // GET: bff/configuration
        [HttpGet]
        public async Task<ActionResult<AppConfiguration>> GetConfiguration()
        {
            try
            {
                _logger.LogInformation("Fetching application configuration from API");
                var config = await _apiClient.GetJsonAsync<AppConfiguration>("configuration");
                
                if (config == null)
                {
                    return StatusCode(500, new { message = "Failed to load configuration" });
                }

                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving configuration from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
