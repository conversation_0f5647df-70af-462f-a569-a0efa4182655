using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;
using System.Security.Claims;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/dashboard")]
    [Authorize]
    public class DashboardController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(ApiClientService apiClient, ILogger<DashboardController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<DashboardData>> GetDashboardData()
        {
            try
            {
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                _logger.LogInformation("Loading dashboard data for user: {UserId}, Role: {Role}", userId, userRole);

                // Load all data in parallel from original API endpoints
                var lessonsTask = _apiClient.GetJsonAsync<List<ScheduleEvent>>("lessons");
                var studentsTask = _apiClient.GetJsonAsync<List<Student>>("students");
                var tutorsTask = _apiClient.GetJsonAsync<List<Tutor>>("tutors");
                var subjectsTask = _apiClient.GetJsonAsync<List<Subject>>("subjects");
                var locationsTask = _apiClient.GetJsonAsync<List<Location>>("locations");

                await Task.WhenAll(lessonsTask, studentsTask, tutorsTask, subjectsTask, locationsTask);

                var lessons = (await lessonsTask) ?? new List<ScheduleEvent>();
                var students = (await studentsTask) ?? new List<Student>();
                var tutors = (await tutorsTask) ?? new List<Tutor>();
                var subjects = (await subjectsTask) ?? new List<Subject>();
                var locations = (await locationsTask) ?? new List<Location>();

                // Filter data based on user role
                var lessonsList = lessons.ToList();
                var studentsList = students.ToList();
                var tutorsList = tutors.ToList();
                var subjectsList = subjects.ToList();
                var locationsList = locations.ToList();

                if (userRole == "Tutor")
                {
                    var tutorLoginName = User.Identity?.Name;
                    // Find the tutor by LoginName and filter lessons by TutorId
                    var currentTutor = tutorsList.FirstOrDefault(t => t.LoginName == tutorLoginName);
                    if (currentTutor != null)
                    {
                        lessonsList = lessonsList.Where(l => l.TutorId == currentTutor.TutorId).ToList();
                    }
                    else
                    {
                        lessonsList = new List<ScheduleEvent>(); // No lessons if tutor not found
                    }
                }
                else if (userRole == "Student")
                {
                    var studentLoginName = User.Identity?.Name;
                    // Find the student by LoginName and filter lessons by StudentId
                    var currentStudent = studentsList.FirstOrDefault(s => s.LoginName == studentLoginName);
                    if (currentStudent != null)
                    {
                        lessonsList = lessonsList.Where(l => l.StudentId == currentStudent.StudentId).ToList();
                    }
                    else
                    {
                        lessonsList = new List<ScheduleEvent>(); // No lessons if student not found
                    }
                }

                // Calculate dashboard statistics
                var stats = new DashboardStats
                {
                    TotalLessons = lessonsList.Count(),
                    TotalStudents = studentsList.Count(),
                    TotalTutors = tutorsList.Count(),
                    TotalSubjects = subjectsList.Count(),
                    UpcomingLessons = lessonsList.Count(l => l.StartTime > DateTime.Now),
                    TodayLessons = lessonsList.Count(l => l.StartTime.Date == DateTime.Today),
                    ThisWeekLessons = lessonsList.Count(l =>
                        l.StartTime >= DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek) &&
                        l.StartTime < DateTime.Today.AddDays(7 - (int)DateTime.Today.DayOfWeek))
                };

                var dashboardData = new DashboardData
                {
                    Lessons = lessonsList,
                    Students = studentsList,
                    Tutors = tutorsList,
                    Subjects = subjectsList,
                    Locations = locationsList,
                    Stats = stats,
                    UserRole = userRole ?? "Unknown",
                    LoadedAt = DateTime.UtcNow
                };

                _logger.LogInformation("Dashboard data loaded successfully for user: {UserId}", userId);
                return Ok(dashboardData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard data from API");
                return StatusCode(500, new { message = "Error loading dashboard data" });
            }
        }

        [HttpGet("quick-stats")]
        public async Task<ActionResult<DashboardStats>> GetQuickStats()
        {
            try
            {
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                _logger.LogInformation("Loading quick stats for user: {UserId}, Role: {Role}", userId, userRole);

                var lessons = await _apiClient.GetJsonAsync<List<ScheduleEvent>>("lessons");
                var lessonsList = (lessons ?? new List<ScheduleEvent>()).ToList();

                // Filter lessons based on user role
                if (userRole == "Tutor")
                {
                    var tutorLoginName = User.Identity?.Name;
                    lessonsList = lessonsList.Where(l => l.TutorName == tutorLoginName).ToList();
                }
                else if (userRole == "Student")
                {
                    var studentLoginName = User.Identity?.Name;
                    lessonsList = lessonsList.Where(l => l.StudentName == studentLoginName).ToList();
                }

                var stats = new DashboardStats
                {
                    TotalLessons = lessonsList.Count(),
                    UpcomingLessons = lessonsList.Count(l => l.StartTime > DateTime.Now),
                    TodayLessons = lessonsList.Count(l => l.StartTime.Date == DateTime.Today),
                    ThisWeekLessons = lessonsList.Count(l =>
                        l.StartTime >= DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek) &&
                        l.StartTime < DateTime.Today.AddDays(7 - (int)DateTime.Today.DayOfWeek))
                };

                // For admin users, get total counts
                if (userRole == "Administrator")
                {
                    var studentsTask = _apiClient.GetJsonAsync<List<Student>>("students");
                    var tutorsTask = _apiClient.GetJsonAsync<List<Tutor>>("tutors");
                    var subjectsTask = _apiClient.GetJsonAsync<List<Subject>>("subjects");

                    await Task.WhenAll(studentsTask, tutorsTask, subjectsTask);

                    var students = (await studentsTask) ?? new List<Student>();
                    var tutors = (await tutorsTask) ?? new List<Tutor>();
                    var subjects = (await subjectsTask) ?? new List<Subject>();

                    stats.TotalStudents = students.Count();
                    stats.TotalTutors = tutors.Count();
                    stats.TotalSubjects = subjects.Count();
                }

                _logger.LogInformation("Quick stats loaded successfully for user: {UserId}", userId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading quick stats from API");
                return StatusCode(500, new { message = "Error loading quick stats" });
            }
        }


    }


}
