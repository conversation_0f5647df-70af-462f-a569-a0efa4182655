using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/lessons")]
    [Authorize]
    public class LessonsController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<LessonsController> _logger;

        public LessonsController(ApiClientService apiClient, ILogger<LessonsController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // GET: bff/lessons
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ScheduleEvent>>> GetLessons()
        {
            try
            {
                _logger.LogInformation("Fetching lessons from API");
                var lessons = await _apiClient.GetJsonAsync<List<ScheduleEvent>>("lessons");
                return Ok(lessons ?? new List<ScheduleEvent>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving lessons from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/lessons/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<ScheduleEvent>> GetLesson(int id)
        {
            try
            {
                _logger.LogInformation("Fetching lesson {LessonId} from API", id);
                var lesson = await _apiClient.GetJsonAsync<ScheduleEvent>($"lessons/{id}");
                
                if (lesson == null)
                {
                    return NotFound(new { message = "Lesson not found" });
                }

                return Ok(lesson);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving lesson {LessonId} from API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/lessons
        [HttpPost]
        public async Task<ActionResult<ScheduleEvent>> CreateLesson([FromBody] ScheduleEvent lesson)
        {
            try
            {
                _logger.LogInformation("Creating lesson via API");
                var response = await _apiClient.PostAsync("lessons", lesson);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdLesson = System.Text.Json.JsonSerializer.Deserialize<ScheduleEvent>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return CreatedAtAction(nameof(GetLesson), new { id = createdLesson?.Id }, createdLesson);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create lesson. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to create lesson", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating lesson via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/lessons/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLesson(int id, [FromBody] ScheduleEvent lesson)
        {
            if (id != lesson.Id)
            {
                return BadRequest(new { message = "Lesson ID mismatch" });
            }

            try
            {
                _logger.LogInformation("Updating lesson {LessonId} via API", id);
                var response = await _apiClient.PutAsync($"lessons/{id}", lesson);
                
                if (response.IsSuccessStatusCode)
                {
                    return NoContent();
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update lesson {LessonId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Lesson not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to update lesson", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating lesson {LessonId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/lessons/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLesson(int id)
        {
            try
            {
                _logger.LogInformation("Deleting lesson {LessonId} via API", id);
                var response = await _apiClient.DeleteAsync($"lessons/{id}");
                
                if (response.IsSuccessStatusCode)
                {
                    return NoContent();
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete lesson {LessonId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Lesson not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete lesson", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting lesson {LessonId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/lessons/export-ics
        [HttpGet("export-ics")]
        public async Task<IActionResult> ExportIcs()
        {
            try
            {
                _logger.LogInformation("Exporting lessons as ICS via API");
                var response = await _apiClient.GetAsync("lessons/export-ics");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsByteArrayAsync();
                    return File(content, "text/calendar", "lessons.ics");
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to export ICS. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to export ICS", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting ICS via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/lessons/import-ics
        [HttpPost("import-ics")]
        public async Task<IActionResult> ImportIcs([FromForm] IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { message = "No file provided" });
                }

                _logger.LogInformation("Importing ICS file via API");
                
                using var content = new MultipartFormDataContent();
                using var fileContent = new StreamContent(file.OpenReadStream());
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(file.ContentType ?? "text/calendar");
                content.Add(fileContent, "file", file.FileName);

                var response = await _apiClient.PostAsync("lessons/import-ics", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    return Ok(new { message = "ICS file imported successfully", details = result });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to import ICS. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to import ICS", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing ICS via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
