using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/locations")]
    [Authorize]
    public class LocationsController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<LocationsController> _logger;

        public LocationsController(ApiClientService apiClient, ILogger<LocationsController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // GET: bff/locations
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Location>>> GetLocations()
        {
            try
            {
                _logger.LogInformation("Fetching locations from API");
                var locations = await _apiClient.GetJsonAsync<List<Location>>("locations");
                return Ok(locations ?? new List<Location>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving locations from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/locations/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Location>> GetLocation(int id)
        {
            try
            {
                _logger.LogInformation("Fetching location {LocationId} from API", id);
                var location = await _apiClient.GetJsonAsync<Location>($"locations/{id}");
                
                if (location == null)
                {
                    return NotFound(new { message = "Location not found" });
                }

                return Ok(location);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving location {LocationId} from API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/locations
        [HttpPost]
        public async Task<ActionResult<Location>> CreateLocation([FromBody] Location location)
        {
            try
            {
                _logger.LogInformation("Creating location via API");
                var response = await _apiClient.PostAsync("locations", location);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdLocation = System.Text.Json.JsonSerializer.Deserialize<Location>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return CreatedAtAction(nameof(GetLocation), new { id = createdLocation?.LocationId }, createdLocation);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create location. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to create location", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating location via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/locations/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLocation(int id, [FromBody] Location location)
        {
            try
            {
                _logger.LogInformation("Updating location {LocationId} via API", id);
                var response = await _apiClient.PutAsync($"locations/{id}", location);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Location updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update location {LocationId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Location not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to update location", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating location {LocationId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/locations/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLocation(int id)
        {
            try
            {
                _logger.LogInformation("Deleting location {LocationId} via API", id);
                var response = await _apiClient.DeleteAsync($"locations/{id}");
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "Location deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete location {LocationId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Location not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete location", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting location {LocationId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
