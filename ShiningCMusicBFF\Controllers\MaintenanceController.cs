using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/maintenance")]
    [Authorize]
    public class MaintenanceController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<MaintenanceController> _logger;

        public MaintenanceController(ApiClientService apiClient, ILogger<MaintenanceController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // POST: bff/maintenance/cleanup-all-lessons
        [HttpPost("cleanup-all-lessons")]
        public async Task<IActionResult> CleanupAllLessons([FromQuery] int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Manual cleanup requested for lessons older than {Days} days", olderThanDays);
                var response = await _apiClient.PostAsync($"maintenance/cleanup-all-lessons?olderThanDays={olderThanDays}", null);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<object>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return Ok(result);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to cleanup lessons. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to cleanup lessons", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during lesson cleanup via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/maintenance/all-lessons-count
        [HttpGet("all-lessons-count")]
        public async Task<IActionResult> GetAllLessonsCount([FromQuery] int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Getting count of lessons older than {Days} days", olderThanDays);
                var result = await _apiClient.GetJsonAsync<object>($"maintenance/all-lessons-count?olderThanDays={olderThanDays}");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lessons count via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/maintenance/cleanup-archived-tutors
        [HttpPost("cleanup-archived-tutors")]
        public async Task<IActionResult> CleanupArchivedTutors([FromQuery] int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Manual cleanup requested for archived tutors older than {Days} days", olderThanDays);
                var response = await _apiClient.PostAsync($"maintenance/cleanup-archived-tutors?olderThanDays={olderThanDays}", null);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<object>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return Ok(result);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to cleanup archived tutors. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to cleanup archived tutors", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during archived tutors cleanup via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/maintenance/archived-tutors-count
        [HttpGet("archived-tutors-count")]
        public async Task<IActionResult> GetArchivedTutorsCount([FromQuery] int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Getting count of archived tutors older than {Days} days", olderThanDays);
                var result = await _apiClient.GetJsonAsync<object>($"maintenance/archived-tutors-count?olderThanDays={olderThanDays}");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting archived tutors count via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/maintenance/cleanup-archived-students
        [HttpPost("cleanup-archived-students")]
        public async Task<IActionResult> CleanupArchivedStudents([FromQuery] int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Manual cleanup requested for archived students older than {Days} days", olderThanDays);
                var response = await _apiClient.PostAsync($"maintenance/cleanup-archived-students?olderThanDays={olderThanDays}", null);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = System.Text.Json.JsonSerializer.Deserialize<object>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return Ok(result);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to cleanup archived students. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to cleanup archived students", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during archived students cleanup via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/maintenance/archived-students-count
        [HttpGet("archived-students-count")]
        public async Task<IActionResult> GetArchivedStudentsCount([FromQuery] int olderThanDays = 30)
        {
            try
            {
                _logger.LogInformation("Getting count of archived students older than {Days} days", olderThanDays);
                var result = await _apiClient.GetJsonAsync<object>($"maintenance/archived-students-count?olderThanDays={olderThanDays}");
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting archived students count via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
