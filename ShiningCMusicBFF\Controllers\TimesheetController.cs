using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;
using System.Security.Claims;
using System.Text.Json;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/timesheet")]
    [Authorize]
    public class TimesheetController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<TimesheetController> _logger;

        public TimesheetController(ApiClientService apiClient, ILogger<TimesheetController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        [HttpGet("data")]
        public async Task<IActionResult> GetTimesheetData()
        {
            try
            {
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var loginName = User.Identity?.Name;

                _logger.LogInformation("Loading timesheet data from API for user {LoginName} with role {UserRole}", loginName, userRole);

                // Load reference data in parallel
                var studentsTask = _apiClient.GetJsonAsync<List<Student>>("students");
                var tutorsTask = _apiClient.GetJsonAsync<List<Tutor>>("tutors");
                var subjectsTask = _apiClient.GetJsonAsync<List<Subject>>("subjects");

                await Task.WhenAll(studentsTask, tutorsTask, subjectsTask);

                var students = await studentsTask ?? new List<Student>();
                var tutors = await tutorsTask ?? new List<Tutor>();
                var subjects = await subjectsTask ?? new List<Subject>();

                // Load timesheets based on role
                List<Timesheet> timesheets;
                if (userRole == "Administrator")
                {
                    timesheets = await _apiClient.GetJsonAsync<List<Timesheet>>("timesheets") ?? new List<Timesheet>();
                }
                else if (userRole == "Tutor" && !string.IsNullOrEmpty(loginName))
                {
                    // Find tutor ID for current user
                    var tutor = tutors.FirstOrDefault(t => t.LoginName == loginName);
                    if (tutor != null)
                    {
                        timesheets = await _apiClient.GetJsonAsync<List<Timesheet>>($"timesheets/tutor/{tutor.TutorId}") ?? new List<Timesheet>();
                    }
                    else
                    {
                        timesheets = new List<Timesheet>();
                    }
                }
                else
                {
                    timesheets = new List<Timesheet>();
                }

                return Ok(new TimesheetData
                {
                    Timesheets = timesheets,
                    Students = students,
                    Tutors = tutors,
                    Subjects = subjects
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading timesheet data from API");
                return StatusCode(500, new { message = "An error occurred loading timesheet data" });
            }
        }

        [HttpGet("{timesheetId}/entries")]
        public async Task<IActionResult> GetTimesheetEntries(int timesheetId)
        {
            try
            {
                _logger.LogInformation("Loading timesheet entries from API for timesheet {TimesheetId}", timesheetId);
                
                var entries = await _apiClient.GetJsonAsync<List<TimesheetEntry>>($"timesheets/{timesheetId}/entries");
                return Ok(entries ?? new List<TimesheetEntry>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading timesheet entries for timesheet {TimesheetId} from API", timesheetId);
                return StatusCode(500, new { message = "An error occurred loading timesheet entries" });
            }
        }

        [HttpPost]
        [Authorize(Roles = "Administrator,Tutor")]
        public async Task<IActionResult> CreateTimesheet([FromBody] Timesheet timesheet)
        {
            try
            {
                _logger.LogInformation("Creating timesheet via API");
                
                var response = await _apiClient.PostAsync("timesheets", timesheet);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdTimesheet = JsonSerializer.Deserialize<Timesheet>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return Ok(new { success = true, message = "Timesheet created successfully", timesheet = createdTimesheet });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var messageProp) ? messageProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }
                
                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create timesheet. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, failureContent);
                return BadRequest(new { message = "Failed to create timesheet" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating timesheet via API");
                return StatusCode(500, new { message = "An error occurred creating timesheet" });
            }
        }

        [HttpPut("{timesheetId}")]
        [Authorize(Roles = "Administrator,Tutor")]
        public async Task<IActionResult> UpdateTimesheet(int timesheetId, [FromBody] Timesheet timesheet)
        {
            try
            {
                _logger.LogInformation("Updating timesheet via API: {TimesheetId}", timesheetId);
                
                // Ensure the timesheet ID matches
                timesheet.TimesheetId = timesheetId;
                
                var response = await _apiClient.PutAsync($"timesheets/{timesheetId}", timesheet);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Timesheet updated successfully" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Timesheet not found" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var messageProp) ? messageProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }
                
                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update timesheet. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, failureContent);
                return BadRequest(new { message = "Failed to update timesheet" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating timesheet {TimesheetId} via API", timesheetId);
                return StatusCode(500, new { message = "An error occurred updating timesheet" });
            }
        }

        [HttpDelete("{timesheetId}")]
        [Authorize(Roles = "Administrator")]
        public async Task<IActionResult> DeleteTimesheet(int timesheetId)
        {
            try
            {
                _logger.LogInformation("Deleting timesheet via API: {TimesheetId}", timesheetId);
                
                var response = await _apiClient.DeleteAsync($"timesheets/{timesheetId}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Timesheet deleted successfully" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Timesheet not found" });
                }
                
                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete timesheet. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, failureContent);
                return BadRequest(new { message = "Failed to delete timesheet" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting timesheet {TimesheetId} via API", timesheetId);
                return StatusCode(500, new { message = "An error occurred deleting timesheet" });
            }
        }

        [HttpPost("entries")]
        public async Task<IActionResult> CreateTimesheetEntry([FromBody] TimesheetEntry entry)
        {
            try
            {
                _logger.LogInformation("Creating timesheet entry via API");
                
                var response = await _apiClient.PostAsync("timesheets/entries", entry);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdEntry = JsonSerializer.Deserialize<TimesheetEntry>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return Ok(createdEntry);
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var messageProp) ? messageProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }
                
                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create timesheet entry. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, failureContent);
                return BadRequest(new { message = "Failed to create timesheet entry" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating timesheet entry via API");
                return StatusCode(500, new { message = "An error occurred creating timesheet entry" });
            }
        }

        [HttpPut("entries/{entryId}")]
        public async Task<IActionResult> UpdateTimesheetEntry(int entryId, [FromBody] TimesheetEntry entry)
        {
            try
            {
                _logger.LogInformation("Updating timesheet entry via API: {EntryId}", entryId);

                if (entryId != entry.TimesheetEntryId)
                {
                    return BadRequest(new { message = "Entry ID mismatch" });
                }

                var response = await _apiClient.PutAsync($"timesheets/entries/{entryId}", entry);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Timesheet entry updated successfully" });
                }

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Timesheet entry not found" });
                }

                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var messageProp) ? messageProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }

                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update timesheet entry. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, failureContent);
                return BadRequest(new { message = "Failed to update timesheet entry" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating timesheet entry {EntryId} via API", entryId);
                return StatusCode(500, new { message = "An error occurred updating timesheet entry" });
            }
        }

        [HttpDelete("entries/{entryId}")]
        public async Task<IActionResult> DeleteTimesheetEntry(int entryId)
        {
            try
            {
                _logger.LogInformation("Deleting timesheet entry via API: {EntryId}", entryId);

                var response = await _apiClient.DeleteAsync($"timesheets/entries/{entryId}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Timesheet entry deleted successfully" });
                }

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Timesheet entry not found" });
                }

                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete timesheet entry. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, failureContent);
                return BadRequest(new { message = "Failed to delete timesheet entry" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting timesheet entry {EntryId} via API", entryId);
                return StatusCode(500, new { message = "An error occurred deleting timesheet entry" });
            }
        }

        [HttpPost("entries/bulk")]
        public async Task<IActionResult> CreateMultipleTimesheetEntries([FromBody] BffCreateMultipleEntriesRequest request)
        {
            try
            {
                _logger.LogInformation("Creating multiple timesheet entries via API: {TimesheetId}, {NumberOfEntries}",
                    request.TimesheetId, request.NumberOfEntries);

                // Convert BFF request to original API request format
                var apiRequest = new
                {
                    timesheetId = request.TimesheetId,
                    numberOfRecords = request.NumberOfEntries
                };

                var response = await _apiClient.PostAsync("timesheets/entries/multiple", apiRequest);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var entries = JsonSerializer.Deserialize<List<TimesheetEntry>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return Ok(entries ?? new List<TimesheetEntry>());
                }

                if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    var errorMessage = errorResult.TryGetProperty("message", out var messageProp) ? messageProp.GetString() : "Bad request";
                    return BadRequest(new { message = errorMessage });
                }

                var failureContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create multiple timesheet entries. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, failureContent);
                return StatusCode(500, new { message = "An error occurred creating multiple timesheet entries" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating multiple timesheet entries via API");
                return StatusCode(500, new { message = "An error occurred creating multiple timesheet entries" });
            }
        }
    }

    public class TimesheetData
    {
        public List<Timesheet> Timesheets { get; set; } = new();
        public List<Student> Students { get; set; } = new();
        public List<Tutor> Tutors { get; set; } = new();
        public List<Subject> Subjects { get; set; } = new();
    }

    public class BffCreateMultipleEntriesRequest
    {
        public int TimesheetId { get; set; }
        public int NumberOfEntries { get; set; }
    }
}
