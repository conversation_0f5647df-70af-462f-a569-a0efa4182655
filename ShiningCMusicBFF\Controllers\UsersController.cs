using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/users")]
    [Authorize]
    public class UsersController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<UsersController> _logger;

        public UsersController(ApiClientService apiClient, ILogger<UsersController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        // GET: bff/users
        [HttpGet]
        public async Task<ActionResult<IEnumerable<User>>> GetUsers()
        {
            try
            {
                _logger.LogInformation("Fetching users from API");
                var users = await _apiClient.GetJsonAsync<List<User>>("user");
                return Ok(users ?? new List<User>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving users from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/users/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<User>> GetUser(int id)
        {
            try
            {
                _logger.LogInformation("Fetching user {UserId} from API", id);
                var user = await _apiClient.GetJsonAsync<User>($"user/{id}");
                
                if (user == null)
                {
                    return NotFound(new { message = "User not found" });
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user {UserId} from API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/users
        [HttpPost]
        public async Task<ActionResult<User>> CreateUser([FromBody] User user)
        {
            try
            {
                _logger.LogInformation("Creating user via API");
                var response = await _apiClient.PostAsync("user", user);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdUser = System.Text.Json.JsonSerializer.Deserialize<User>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return CreatedAtAction(nameof(GetUser), new { id = createdUser?.UserName }, createdUser);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create user. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to create user", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/users/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUser(int id, [FromBody] User user)
        {
            try
            {
                _logger.LogInformation("Updating user {UserId} via API", id);
                var response = await _apiClient.PutAsync($"user/{id}", user);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "User updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update user {UserId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "User not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to update user", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/users/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUser(int id)
        {
            try
            {
                _logger.LogInformation("Deleting user {UserId} via API", id);
                var response = await _apiClient.DeleteAsync($"user/{id}");
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "User deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete user {UserId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "User not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete user", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // GET: bff/users/roles
        [HttpGet("roles")]
        public async Task<ActionResult<IEnumerable<UserRole>>> GetUserRoles()
        {
            try
            {
                _logger.LogInformation("Fetching user roles from API");
                var roles = await _apiClient.GetJsonAsync<List<UserRole>>("user/roles");
                return Ok(roles ?? new List<UserRole>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user roles from API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // POST: bff/users/roles
        [HttpPost("roles")]
        public async Task<ActionResult<UserRole>> CreateUserRole([FromBody] UserRole userRole)
        {
            try
            {
                _logger.LogInformation("Creating user role via API");
                var response = await _apiClient.PostAsync("user/roles", userRole);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdRole = System.Text.Json.JsonSerializer.Deserialize<UserRole>(content, new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return Ok(createdRole);
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create user role. Status: {StatusCode}, Error: {Error}", response.StatusCode, errorContent);
                return StatusCode((int)response.StatusCode, new { message = "Failed to create user role", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user role via API");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // PUT: bff/users/roles/{id}
        [HttpPut("roles/{id}")]
        public async Task<IActionResult> UpdateUserRole(int id, [FromBody] UserRole userRole)
        {
            try
            {
                _logger.LogInformation("Updating user role {RoleId} via API", id);
                var response = await _apiClient.PutAsync($"user/roles/{id}", userRole);
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "User role updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update user role {RoleId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "User role not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to update user role", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user role {RoleId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        // DELETE: bff/users/roles/{id}
        [HttpDelete("roles/{id}")]
        public async Task<IActionResult> DeleteUserRole(int id)
        {
            try
            {
                _logger.LogInformation("Deleting user role {RoleId} via API", id);
                var response = await _apiClient.DeleteAsync($"user/roles/{id}");
                
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { message = "User role deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete user role {RoleId}. Status: {StatusCode}, Error: {Error}", id, response.StatusCode, errorContent);
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "User role not found" });
                }

                return StatusCode((int)response.StatusCode, new { message = "Failed to delete user role", details = errorContent });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user role {RoleId} via API", id);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }
    }
}
