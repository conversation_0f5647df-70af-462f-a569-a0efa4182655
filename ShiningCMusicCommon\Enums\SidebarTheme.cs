using System.ComponentModel;

namespace ShiningCMusicCommon.Enums
{
    public enum SidebarTheme
    {
        [Description("Blue to Purple (Default)")]
        BluePurple = 1,

        [Description("Blue Gray")]
        BlueGray = 2,

        [Description("Purple Gradient")]
        Purple = 3,

        [Description("Dark Gray")]
        DarkGray = 4,

        [Description("Navy Blue")]
        NavyBlue = 5,

        [Description("Teal Gradient")]
        Teal = 6,

        [Description("Dark Blue")]
        DarkBlue = 7,

        [Description("Gold to Amber")]
        GoldAmber = 8,

        [Description("Silver to Charcoal")]
        SilverCharcoal = 9,

        [Description("Deep Purple to Indigo")]
        DeepPurpleIndigo = 10,

        [Description("Midnight Blue to Steel")]
        MidnightSteel = 11,

        [Description("Elegant Black to Gray")]
        ElegantBlackGray = 12,

        [Description("Custom Colors")]
        Custom = 13
    }
    
    public static class SidebarThemeExtensions
    {
        public static string GetCssGradient(this SidebarTheme theme)
        {
            return theme switch
            {
                SidebarTheme.BluePurple => "linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%)",
                SidebarTheme.BlueGray => "linear-gradient(180deg, #2c3e50 0%, #34495e 70%)",
                SidebarTheme.Purple => "linear-gradient(180deg, #8e44ad 0%, #9b59b6 70%)",
                SidebarTheme.DarkGray => "linear-gradient(180deg, #34495e 0%, #2c3e50 70%)",
                SidebarTheme.NavyBlue => "linear-gradient(180deg, #2c3e50 0%, #1a252f 70%)",
                SidebarTheme.Teal => "linear-gradient(180deg, #16a085 0%, #1abc9c 70%)",
                SidebarTheme.DarkBlue => "linear-gradient(180deg, #1a252f 0%, #2c3e50 70%)",
                SidebarTheme.GoldAmber => "linear-gradient(180deg, #b8860b 0%, #ff8c00 70%)",
                SidebarTheme.SilverCharcoal => "linear-gradient(180deg, #708090 0%, #2f4f4f 70%)",
                SidebarTheme.DeepPurpleIndigo => "linear-gradient(180deg, #483d8b 0%, #4b0082 70%)",
                SidebarTheme.MidnightSteel => "linear-gradient(180deg, #191970 0%, #4682b4 70%)",
                SidebarTheme.ElegantBlackGray => "linear-gradient(180deg, #2c2c2c 0%, #1a1a1a 70%)",
                SidebarTheme.Custom => "linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%)", // Default, will be overridden
                _ => "linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%)"
            };
        }
        
        public static string GetPreviewColor(this SidebarTheme theme)
        {
            return theme switch
            {
                SidebarTheme.BluePurple => "linear-gradient(90deg, rgb(5, 39, 103) 0%, #3a0647 100%)",
                SidebarTheme.BlueGray => "linear-gradient(90deg, #2c3e50 0%, #34495e 100%)",
                SidebarTheme.Purple => "linear-gradient(90deg, #8e44ad 0%, #9b59b6 100%)",
                SidebarTheme.DarkGray => "linear-gradient(90deg, #34495e 0%, #2c3e50 100%)",
                SidebarTheme.NavyBlue => "linear-gradient(90deg, #2c3e50 0%, #1a252f 100%)",
                SidebarTheme.Teal => "linear-gradient(90deg, #16a085 0%, #1abc9c 100%)",
                SidebarTheme.DarkBlue => "linear-gradient(90deg, #1a252f 0%, #2c3e50 100%)",
                SidebarTheme.GoldAmber => "linear-gradient(90deg, #b8860b 0%, #ff8c00 100%)",
                SidebarTheme.SilverCharcoal => "linear-gradient(90deg, #708090 0%, #2f4f4f 100%)",
                SidebarTheme.DeepPurpleIndigo => "linear-gradient(90deg, #483d8b 0%, #4b0082 100%)",
                SidebarTheme.MidnightSteel => "linear-gradient(90deg, #191970 0%, #4682b4 100%)",
                SidebarTheme.ElegantBlackGray => "linear-gradient(90deg, #2c2c2c 0%, #1a1a1a 100%)",
                SidebarTheme.Custom => "linear-gradient(90deg, #6c5ce7 0%, #a29bfe 100%)", // Custom preview color
                _ => "linear-gradient(90deg, rgb(5, 39, 103) 0%, #3a0647 100%)"
            };
        }
        
        public static string GetDescription(this SidebarTheme theme)
        {
            var field = theme.GetType().GetField(theme.ToString());
            var attribute = field?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                .FirstOrDefault() as DescriptionAttribute;
            return attribute?.Description ?? theme.ToString();
        }
    }
}
