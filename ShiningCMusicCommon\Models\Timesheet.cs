using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class Timesheet
    {
        public int TimesheetId { get; set; }
        
        [Required]
        public int StudentId { get; set; }
        
        [Required]
        public int TutorId { get; set; }
        
        [Required]
        public int SubjectId { get; set; }

        [Required]
        public DateTime StartDate { get; set; }
        
        [StringLength(20)]
        public string? ContactNumber { get; set; }
        
        [Required]
        public int ClassDurationMinutes { get; set; }
        
        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedUTC { get; set; }
        
        public bool IsArchived { get; set; } = false;
        
        [StringLength(500)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual Student? Student { get; set; }
        public virtual Tutor? Tutor { get; set; }
        public virtual Subject? Subject { get; set; }
        public virtual ICollection<TimesheetEntry> TimesheetEntries { get; set; } = new List<TimesheetEntry>();
    }
}
