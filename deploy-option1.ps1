# PowerShell Deployment Script for Option 1 (Same App Service)
# This script builds and prepares deployment package for Azure App Service

param(
    [Parameter(Mandatory=$true)]
    [string]$AppServiceName,
    
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [string]$Configuration = "Release"
)

Write-Host "🚀 Starting deployment for Option 1 (Same App Service)" -ForegroundColor Green
Write-Host "App Service: $AppServiceName" -ForegroundColor Yellow
Write-Host "Resource Group: $ResourceGroupName" -ForegroundColor Yellow

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Cyan
if (Test-Path "./deploy") { Remove-Item -Recurse -Force "./deploy" }
if (Test-Path "./publish") { Remove-Item -Recurse -Force "./publish" }

# Create directories
New-Item -ItemType Directory -Path "./publish" -Force | Out-Null
New-Item -ItemType Directory -Path "./deploy" -Force | Out-Null

# Build solution
Write-Host "🔨 Building solution..." -ForegroundColor Cyan
dotnet build --configuration $Configuration
if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed!"
    exit 1
}

# Publish API project
Write-Host "📦 Publishing API project..." -ForegroundColor Cyan
dotnet publish ShiningCMusicApi -c $Configuration -o "./publish/api" --no-build
if ($LASTEXITCODE -ne 0) {
    Write-Error "API publish failed!"
    exit 1
}

# Publish BFF project
Write-Host "📦 Publishing BFF project..." -ForegroundColor Cyan
dotnet publish ShiningCMusicBFF -c $Configuration -o "./publish/bff" --no-build
if ($LASTEXITCODE -ne 0) {
    Write-Error "BFF publish failed!"
    exit 1
}

# Publish Blazor project
Write-Host "📦 Publishing Blazor project..." -ForegroundColor Cyan
dotnet publish ShiningCMusicApp -c $Configuration -o "./publish/blazor" --no-build
if ($LASTEXITCODE -ne 0) {
    Write-Error "Blazor publish failed!"
    exit 1
}

# Combine for deployment
Write-Host "🔗 Combining projects for deployment..." -ForegroundColor Cyan

# Copy API files as the base
Copy-Item -Path "./publish/api/*" -Destination "./deploy/" -Recurse -Force

# Create BFF subfolder and copy BFF files
New-Item -ItemType Directory -Path "./deploy/bff" -Force | Out-Null
Copy-Item -Path "./publish/bff/*" -Destination "./deploy/bff/" -Recurse -Force

# Copy Blazor static files to wwwroot
if (Test-Path "./publish/blazor/wwwroot") {
    Copy-Item -Path "./publish/blazor/wwwroot/*" -Destination "./deploy/wwwroot/" -Recurse -Force
}

# Create web.config for proper routing
Write-Host "⚙️ Creating web.config for routing..." -ForegroundColor Cyan
$webConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <!-- BFF API routes -->
        <rule name="BFF Routes" stopProcessing="true">
          <match url="^bff/(.*)$" />
          <action type="Rewrite" url="bff/{R:1}" />
        </rule>
        
        <!-- API routes -->
        <rule name="API Routes" stopProcessing="true">
          <match url="^api/(.*)$" />
          <action type="Rewrite" url="api/{R:1}" />
        </rule>
        
        <!-- Blazor fallback -->
        <rule name="Blazor Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(api|bff)" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
    
    <!-- Enable compression -->
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />
    
    <!-- Set proper MIME types -->
    <staticContent>
      <mimeMap fileExtension=".wasm" mimeType="application/wasm" />
      <mimeMap fileExtension=".dll" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".dat" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".blat" mimeType="application/octet-stream" />
    </staticContent>
  </system.webServer>
</configuration>
"@

$webConfig | Out-File -FilePath "./deploy/web.config" -Encoding UTF8

# Create deployment package
Write-Host "📦 Creating deployment package..." -ForegroundColor Cyan
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$packageName = "deployment-package-$timestamp.zip"

# Compress deployment folder
Compress-Archive -Path "./deploy/*" -DestinationPath "./$packageName" -Force

Write-Host "✅ Deployment package created: $packageName" -ForegroundColor Green

# Deploy to Azure (if Azure CLI is available)
if (Get-Command az -ErrorAction SilentlyContinue) {
    Write-Host "🌐 Deploying to Azure App Service..." -ForegroundColor Cyan
    
    # Check if logged in to Azure
    $azAccount = az account show 2>$null
    if (-not $azAccount) {
        Write-Host "⚠️ Not logged in to Azure. Please run 'az login' first." -ForegroundColor Yellow
        Write-Host "📦 Deployment package ready: $packageName" -ForegroundColor Green
        Write-Host "🔧 Manual deployment: Upload $packageName to Azure Portal" -ForegroundColor Yellow
        exit 0
    }
    
    # Deploy using Azure CLI
    az webapp deployment source config-zip --resource-group $ResourceGroupName --name $AppServiceName --src $packageName
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "🎉 Deployment successful!" -ForegroundColor Green
        Write-Host "🌐 Your app should be available at: https://$AppServiceName.azurewebsites.net" -ForegroundColor Green
    } else {
        Write-Error "Deployment failed! Check Azure portal for details."
    }
} else {
    Write-Host "⚠️ Azure CLI not found. Manual deployment required." -ForegroundColor Yellow
    Write-Host "📦 Deployment package ready: $packageName" -ForegroundColor Green
    Write-Host "🔧 Upload this package to Azure Portal → App Service → Deployment Center" -ForegroundColor Yellow
}

# Cleanup
Write-Host "🧹 Cleaning up temporary files..." -ForegroundColor Cyan
Remove-Item -Recurse -Force "./publish"
Remove-Item -Recurse -Force "./deploy"

Write-Host "✅ Deployment script completed!" -ForegroundColor Green
