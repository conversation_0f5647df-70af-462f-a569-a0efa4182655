# Service Separation Guide: Option 1 → Option 2

This guide shows you exactly how to migrate from Option 1 (same App Service) to Option 2 (separate services) when you're ready.

## 🎯 When to Consider Separation

### Performance Indicators
- **API under heavy load** but BFF is fine → Separate to scale API independently
- **B<PERSON> needs more resources** for aggregation logic → Separate to scale BFF independently
- **Different deployment schedules** needed for API vs BFF → Separate for independent releases

### Cost vs Benefit Analysis
- **Current Cost**: ~$13/month (1 App Service)
- **Separated Cost**: ~$26/month (2 App Services) or ~$13/month (API + Static Web Apps)
- **Break-even point**: When independent scaling saves more than $13/month in resources

## 🚀 Migration Steps

### Step 1: Create Infrastructure

```bash
# Login to Azure
az login

# Set variables (customize these)
RESOURCE_GROUP="rg-shiningcmusic"
LOCATION="eastus"
API_APP_NAME="shiningcmusic-api"
BFF_APP_NAME="shiningcmusic-bff"
STATIC_APP_NAME="shiningcmusic-app"

# Create separate App Service plans
az appservice plan create \
  --name "plan-api" \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --sku B1 \
  --is-linux false

az appservice plan create \
  --name "plan-bff" \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --sku B1 \
  --is-linux false

# Create App Services
az webapp create \
  --name $API_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --plan "plan-api" \
  --runtime "DOTNET|8.0"

az webapp create \
  --name $BFF_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --plan "plan-bff" \
  --runtime "DOTNET|8.0"

# Create Static Web App for Blazor
az staticwebapp create \
  --name $STATIC_APP_NAME \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION
```

### Step 2: Update Code Configuration

#### 2.1 Update Blazor Client URL Detection

Edit `ShiningCMusicApp/Program.cs`:

```csharp
static string DetermineApiBaseUrl(WebAssemblyHostBuilder builder, Dictionary<string, string>? localConfig)
{
    var baseAddress = builder.HostEnvironment.BaseAddress;
    
    if (baseAddress.Contains("azurestaticapps.net"))
    {
        // Production - separate API service
        return "https://shiningcmusic-api.azurewebsites.net/api";
    }
    else if (baseAddress.Contains("azurewebsites.net"))
    {
        // Staging/testing - same service (Option 1)
        var baseUri = new Uri(baseAddress);
        return $"{baseUri.Scheme}://{baseUri.Host}/api";
    }
    
    // Local development
    return localConfig?.GetValueOrDefault("ApiBaseUrl") ?? "https://localhost:7268/api";
}

static string DetermineBffBaseUrl(WebAssemblyHostBuilder builder, Dictionary<string, string>? localConfig)
{
    var baseAddress = builder.HostEnvironment.BaseAddress;
    
    if (baseAddress.Contains("azurestaticapps.net"))
    {
        // Production - separate BFF service
        return "https://shiningcmusic-bff.azurewebsites.net/bff";
    }
    else if (baseAddress.Contains("azurewebsites.net"))
    {
        // Staging/testing - same service (Option 1)
        var baseUri = new Uri(baseAddress);
        return $"{baseUri.Scheme}://{baseUri.Host}/bff";
    }
    
    // Local development
    return localConfig?.GetValueOrDefault("BffBaseUrl") ?? "https://localhost:7269/bff";
}
```

#### 2.2 Update BFF CORS Configuration

Edit `ShiningCMusicBFF/Program.cs`:

```csharp
// Configure CORS for separate hosting
var allowedOrigins = new List<string>();

// Check environment variables first
var envOrigins = Environment.GetEnvironmentVariable("CORS_ALLOWED_ORIGINS");
if (!string.IsNullOrEmpty(envOrigins))
{
    allowedOrigins.AddRange(envOrigins.Split(',', StringSplitOptions.RemoveEmptyEntries));
}
else
{
    // Default origins for separate hosting
    allowedOrigins.AddRange(new[] 
    { 
        "https://shiningcmusic-app.azurestaticapps.net",
        "https://localhost:7091" // Local development
    });
}

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowBlazorApp", policy =>
    {
        policy.WithOrigins(allowedOrigins.ToArray())
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials(); // Required for cookie authentication
    });
});
```

### Step 3: Configure Environment Variables

#### 3.1 API App Service Environment Variables

```bash
# Database and core API configuration
az webapp config appsettings set --resource-group $RESOURCE_GROUP --name $API_APP_NAME --settings \
  CONNECTIONSTRINGS__DEFAULTCONNECTION="Server=your-server.database.windows.net;Database=ShiningCMusicDB;..." \
  OPENIDDICT__CLIENT_ID="shining-c-music-client" \
  OPENIDDICT__CLIENT_SECRET="your-api-client-secret" \
  EMAIL__SMTP_HOST="smtp.gmail.com" \
  EMAIL__SMTP_PORT="587" \
  EMAIL__SMTP_USERNAME="<EMAIL>" \
  EMAIL__SMTP_PASSWORD="your-app-password" \
  EMAIL__FROM_EMAIL="<EMAIL>" \
  EMAIL__FROM_NAME="Shining C Music" \
  SYNCFUSION_LICENSE="your-license-key" \
  CORS_ALLOWED_ORIGINS="https://shiningcmusic-bff.azurewebsites.net,https://shiningcmusic-app.azurestaticapps.net"
```

#### 3.2 BFF App Service Environment Variables

```bash
# BFF-specific configuration
az webapp config appsettings set --resource-group $RESOURCE_GROUP --name $BFF_APP_NAME --settings \
  API_BASE_URL="https://shiningcmusic-api.azurewebsites.net/api" \
  BFF_CLIENT_ID="bff-client" \
  BFF_CLIENT_SECRET="your-bff-client-secret" \
  CORS_ALLOWED_ORIGINS="https://shiningcmusic-app.azurestaticapps.net"
```

### Step 4: Create Deployment Scripts

#### 4.1 API Deployment Script (`deploy-api.ps1`)

```powershell
param([string]$AppName = "shiningcmusic-api", [string]$ResourceGroup = "rg-shiningcmusic")

Write-Host "Deploying API to $AppName..." -ForegroundColor Green

# Build and publish API only
dotnet publish ShiningCMusicApi -c Release -o "./publish/api"

# Create deployment package
Compress-Archive -Path "./publish/api/*" -DestinationPath "./api-package.zip" -Force

# Deploy to Azure
az webapp deployment source config-zip --resource-group $ResourceGroup --name $AppName --src "./api-package.zip"

# Cleanup
Remove-Item -Recurse -Force "./publish"
Remove-Item "./api-package.zip"

Write-Host "API deployment completed!" -ForegroundColor Green
```

#### 4.2 BFF Deployment Script (`deploy-bff.ps1`)

```powershell
param([string]$AppName = "shiningcmusic-bff", [string]$ResourceGroup = "rg-shiningcmusic")

Write-Host "Deploying BFF to $AppName..." -ForegroundColor Green

# Build and publish BFF only
dotnet publish ShiningCMusicBFF -c Release -o "./publish/bff"

# Create deployment package
Compress-Archive -Path "./publish/bff/*" -DestinationPath "./bff-package.zip" -Force

# Deploy to Azure
az webapp deployment source config-zip --resource-group $ResourceGroup --name $AppName --src "./bff-package.zip"

# Cleanup
Remove-Item -Recurse -Force "./publish"
Remove-Item "./bff-package.zip"

Write-Host "BFF deployment completed!" -ForegroundColor Green
```

#### 4.3 Blazor Deployment (GitHub Actions for Static Web Apps)

Create `.github/workflows/azure-static-web-apps.yml`:

```yaml
name: Azure Static Web Apps CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches: [ main ]

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
      
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          action: "upload"
          app_location: "ShiningCMusicApp"
          output_location: "wwwroot"
```

### Step 5: Migration Checklist

- [ ] **Infrastructure Created**: API, BFF, and Static Web Apps services
- [ ] **Code Updated**: URL detection logic updated in Blazor client
- [ ] **Environment Variables Set**: All services configured with correct URLs
- [ ] **CORS Configured**: Cross-origin requests properly allowed
- [ ] **Deployment Scripts Ready**: Separate deployment for each service
- [ ] **DNS/Custom Domains**: Update any custom domain configurations
- [ ] **Monitoring**: Set up Application Insights for each service
- [ ] **Testing**: Verify all functionality works across separate services

### Step 6: Rollback Plan

If issues arise, you can quickly rollback to Option 1:

1. **Revert Blazor URL logic** to use same-service detection
2. **Redeploy to original App Service** using the Option 1 deployment script
3. **Update DNS** to point back to original service
4. **Clean up** separate services to avoid costs

### Cost Optimization Tips

1. **Use B1 tier initially** (~$13/month each) and scale up if needed
2. **Consider Static Web Apps Free tier** for Blazor (saves ~$13/month)
3. **Monitor usage** and downscale if services are underutilized
4. **Use Azure Cost Management** to set up alerts and budgets

## 📊 Comparison Summary

| Aspect | Option 1 (Current) | Option 2 (Separated) |
|--------|-------------------|---------------------|
| **Monthly Cost** | ~$13 | ~$26 (or ~$13 with Static Web Apps) |
| **Deployment Complexity** | Single script | 3 separate deployments |
| **Scaling** | Together only | Independent |
| **Monitoring** | Single dashboard | Per-service monitoring |
| **Fault Tolerance** | Single point of failure | Isolated failures |
| **CORS Complexity** | None | Cross-service configuration |
| **Development** | Simple | More complex |

Choose Option 2 when the benefits of independent scaling and deployment outweigh the additional complexity and cost.
