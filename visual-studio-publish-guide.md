# Visual Studio Publishing Guide for BFF Architecture

## 🎯 Current Setup

You can continue using Visual Studio to publish exactly as you do now! I've modified the API project to automatically include the BFF project when you publish.

## 📋 How It Works Now

When you publish the **ShiningCMusicApi** project through Visual Studio:

1. ✅ **API project** publishes normally to the root
2. ✅ **BFF project** automatically publishes to `/bff` subdirectory  
3. ✅ **Blazor WebAssembly** files are included as static content
4. ✅ **Routing** is handled by the web.config file

## 🚀 Publishing Steps (Same as Before!)

### Option 1: Right-click Publish (Recommended)

1. **Right-click** on `ShiningCMusicApi` project in Solution Explorer
2. **Select "Publish"**
3. **Choose your existing profile**: "ShiningCMusicApp - Zip Deploy"
4. **Click "Publish"**

That's it! The BFF will be automatically included.

### Option 2: Visual Studio Publish Menu

1. **Build** → **Publish ShiningCMusicApi**
2. **Select your profile** and publish

## 📁 What Gets Deployed

```
Azure App Service (shiningcmusicapp.azurewebsites.net)
├── API files (root)                    ← ShiningCMusicApi project
├── bff/                               ← ShiningCMusicBFF project (auto-included)
│   ├── ShiningCMusicBFF.dll
│   ├── appsettings.json
│   └── ... (BFF files)
├── wwwroot/                           ← Blazor WebAssembly files
│   ├── _framework/
│   ├── css/
│   └── index.html
└── web.config                         ← Routing configuration
```

## 🔧 Environment Variables to Set

In Azure Portal → App Service → Configuration → Application Settings:

### Required for BFF to work:
```
API_BASE_URL = https://shiningcmusicapp.azurewebsites.net/api
BFF_CLIENT_ID = bff-client  
BFF_CLIENT_SECRET = your-secure-secret-here
```

### Your existing variables (keep these):
```
CONNECTIONSTRINGS__DEFAULTCONNECTION = your-database-connection
OPENIDDICT__CLIENT_ID = your-client-id
OPENIDDICT__CLIENT_SECRET = your-client-secret
EMAIL__SMTP_HOST = smtp.gmail.com
EMAIL__SMTP_PORT = 587
EMAIL__SMTP_USERNAME = <EMAIL>
EMAIL__SMTP_PASSWORD = your-app-password
EMAIL__FROM_EMAIL = <EMAIL>
EMAIL__FROM_NAME = Shining C Music
SYNCFUSION_LICENSE = your-license-key
CORS_ALLOWED_ORIGINS = https://shiningcmusicapp.azurewebsites.net
```

## 🌐 URL Structure After Publishing

- **Blazor App**: `https://shiningcmusicapp.azurewebsites.net/`
- **API Endpoints**: `https://shiningcmusicapp.azurewebsites.net/api/lessons`
- **BFF Endpoints**: `https://shiningcmusicapp.azurewebsites.net/bff/dashboard`

## ✅ Testing After Deployment

1. **Visit your app**: `https://shiningcmusicapp.azurewebsites.net`
2. **Test login** - should use BFF authentication
3. **Check dashboard** - should load data via BFF
4. **Test admin functions** - should work through BFF
5. **Verify API still works** - direct API calls should still function

## 🔍 Troubleshooting

### If BFF endpoints return 404:
- Check that environment variables are set correctly
- Verify the BFF folder was created during publish
- Check Application Insights logs for errors

### If authentication doesn't work:
- Verify `BFF_CLIENT_ID` and `BFF_CLIENT_SECRET` are set
- Check that `API_BASE_URL` points to the correct location
- Ensure CORS is configured properly

### If Blazor app doesn't load:
- Check that static files are in wwwroot
- Verify web.config routing rules
- Check browser console for errors

## 🎛️ Advanced: Custom Publish Profile (Optional)

If you want more control, you can create a custom publish profile:

1. **Right-click** ShiningCMusicApi → **Publish**
2. **New Profile** → **Azure App Service**
3. **Configure** your settings
4. **Save** as new profile

The MSBuild target will still automatically include the BFF.

## 🔄 What Changed vs Before

| Before | After |
|--------|-------|
| Published API only | Publishes API + BFF automatically |
| Blazor called API directly | Blazor calls BFF, BFF calls API |
| Token-based auth only | Cookie-based auth via BFF |
| Single project publish | Multi-project publish (seamless) |

## 💡 Benefits of This Approach

- ✅ **Same workflow** - no changes to your publishing process
- ✅ **Automatic inclusion** - BFF is always included when you publish API
- ✅ **Single deployment** - everything goes to one App Service
- ✅ **Cost effective** - no additional Azure resources needed
- ✅ **Simple management** - one service to monitor and maintain

## 🚀 Future Migration

When you're ready to separate services later:
1. Create separate App Services for API and BFF
2. Update the publish profiles to target different services
3. Remove the MSBuild target from API project
4. Publish each project separately

The code is already structured to support this migration seamlessly!
